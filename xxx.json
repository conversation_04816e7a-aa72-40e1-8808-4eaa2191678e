{"models": [{"title": "Local DeepSeek Coder", "provider": "ollama", "model": "deepseek-coder", "apiBase": "http://localhost:11434"}, {"title": "Local LLaMA 3", "provider": "ollama", "model": "llama3:8b", "apiBase": "http://localhost:11434"}], "tasks": [{"name": "Refactor Code", "prompt": "Please refactor the selected code to improve readability and structure without changing functionality.", "description": "Clean up and simplify your code."}, {"name": "Explain Code", "prompt": "Explain the purpose and functionality of the selected code in clear language.", "description": "Understand what your code is doing."}, {"name": "Generate Tests", "prompt": "Generate unit tests for the selected code using a relevant test framework.", "description": "Create test coverage for your code."}, {"name": "Add Comments", "prompt": "Add useful inline comments to the selected code.", "description": "Improve documentation and readability."}], "fallbackModel": "Local LLaMA 3"}