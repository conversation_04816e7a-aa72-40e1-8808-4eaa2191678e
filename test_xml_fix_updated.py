#!/usr/bin/env python3

import xml.etree.ElementTree as ET
import subprocess
import os
import sys

# Test the XML generation with the fixed code

def get_hardware_info():
    commands = {
        "Model Identifier": "sysctl -n hw.model",
        "Chip": "sysctl -n machdep.cpu.brand_string",
        "Memory": "sysctl -n hw.memsize | awk '{ printf \"%.0f GB\", $0/1024/1024/1024 }'",
        # SSD Storage: Get all internal disk sizes (supports multiple drives)
        "SSD Storage": "diskutil info /dev/disk0 | grep 'Disk Size' | awk '{print $3 \" \" $4}'",
        # Disk Model: Get all internal disk models using SPStorageDataType (more reliable)
        "Disk Model": "system_profiler SPStorageDataType | grep 'Device Name:' | grep -v 'Disk Image' | awk -F':' '{gsub(/^[ \\t]+|[ \\t]+$/, \"\", $2); print $2}' | sort -u | paste -sd ', ' -",
        "Serial number": "ioreg -c IOPlatformExpertDevice -d 2 | grep -i 'IOPlatformSerialNumber' | awk -F \"\\\"\" '{print $4}'",
        "macOS Version": "sw_vers -productVersion"
    }
    info = {}
    for key, command in commands.items():
        try:
            output = subprocess.check_output(command, shell=True, text=True).strip()
            info[key] = output.strip('\"')
        except subprocess.CalledProcessError:
            info[key] = "Unable to retrieve"
    return info

def get_internal_disk_info():
    """Get information about the internal Apple disk specifically"""
    try:
        # Try multiple system_profiler data types to cover all disk types (SATA, NVMe, PCIe)
        disk_info = {}
        
        # First try SPNVMeDataType for NVMe drives (common in newer Macs)
        try:
            nvme_output = subprocess.check_output("system_profiler SPNVMeDataType", shell=True, text=True)
            if 'Model:' in nvme_output:
                # Parse NVMe output
                lines = nvme_output.split('\n')
                current_disk = {}
                
                for line in lines:
                    line = line.strip()
                    if line.endswith(':') and not line.startswith('NVMe'):
                        # Start of a new disk section
                        current_disk = {'name': line.rstrip(':')}
                    elif 'Model:' in line and current_disk:
                        current_disk['model'] = line.split(':', 1)[1].strip()
                        # Found a model, this is likely the internal disk
                        return {
                            'model': current_disk['model'],
                            'bsd_name': '/dev/disk0',  # Typical for boot disk
                            'is_internal': True
                        }
        except:
            pass
            
        # Then try SPSerialATADataType for SATA drives (older Macs)
        try:
            sata_output = subprocess.check_output("system_profiler SPSerialATADataType", shell=True, text=True)
            
            # Look for Apple SSD with Bay Name (indicates internal)
            lines = sata_output.split('\n')
            current_disk = {}
            internal_disk = None

            for line in lines:
                line = line.strip()
                if line.endswith(':') and ('APPLE' in line or 'SSD' in line):
                    # Start of a new disk section
                    current_disk = {'name': line.rstrip(':')}
                elif 'Model:' in line and current_disk:
                    current_disk['model'] = line.split(':', 1)[1].strip()
                elif 'BSD Name:' in line and current_disk:
                    current_disk['bsd_name'] = line.split(':', 1)[1].strip()
                elif 'Bay Name:' in line and current_disk:
                    current_disk['bay_name'] = line.split(':', 1)[1].strip()
                    # If it has a Bay Name, it's likely internal
                    if 'APPLE' in current_disk.get('model', '').upper():
                        internal_disk = current_disk
                        break

            if internal_disk:
                return {
                    'model': internal_disk.get('model', 'Unknown'),
                    'bsd_name': internal_disk.get('bsd_name', '/dev/disk0'),
                    'is_internal': True
                }
            else:
                # Fallback: look for any Apple SSD
                for line in lines:
                    if 'Model:' in line and 'APPLE' in line.upper():
                        model = line.split(':', 1)[1].strip()
                        return {
                            'model': model,
                            'bsd_name': '/dev/disk1',  # Common for internal Apple SSD
                            'is_internal': True
                        }
        except:
            pass
            
        # Finally, try SPStorageDataType as a last resort (most generic)
        try:
            storage_output = subprocess.check_output("system_profiler SPStorageDataType", shell=True, text=True)
            lines = storage_output.split('\n')
            
            for i, line in enumerate(lines):
                if 'Device Name:' in line:
                    model = line.split(':', 1)[1].strip()
                    # Skip external drives or disk images
                    if 'disk image' not in model.lower() and 'external' not in model.lower():
                        return {
                            'model': model,
                            'bsd_name': '/dev/disk0',
                            'is_internal': True
                        }
        except:
            pass

        # Final fallback: use diskutil to get info about disk0 (usually the boot disk)
        try:
            diskutil_output = subprocess.check_output("diskutil info /dev/disk0 | grep 'Device / Media Name'", shell=True, text=True)
            if diskutil_output:
                model = diskutil_output.split(':', 1)[1].strip()
                return {
                    'model': model,
                    'bsd_name': '/dev/disk0',
                    'is_internal': True
                }
        except:
            pass
            
        # Ultimate fallback
        return {
            'model': 'Unknown Internal Disk',
            'bsd_name': '/dev/disk0',
            'is_internal': False
        }

    except Exception as e:
        print(f"Error getting internal disk info: {e}")
        return {
            'model': 'Unknown Internal Disk',
            'bsd_name': '/dev/disk0',
            'is_internal': False
        }

def test_xml_generation():
    # Get hardware info
    info = get_hardware_info()
    
    # Get internal disk info
    disk_info = get_internal_disk_info()
    disk_model = disk_info.get('model', 'Unknown')
    disk_bsd_name = disk_info.get('bsd_name', '/dev/disk0')
    
    # Get Mac model for comparison
    mac_model = info.get("Model Identifier", "Unknown")
    
    # Create XML structure
    report = ET.Element("blancco_report")
    
    # Add hardware report section
    hardware_report = ET.SubElement(report, "hardware_report")
    
    # Add system section
    system = ET.SubElement(hardware_report, "system")
    ET.SubElement(system, "entry", name="manufacturer", type="string").text = "Apple"
    ET.SubElement(system, "entry", name="model", type="string").text = mac_model
    
    # Add processor section
    processor = ET.SubElement(hardware_report, "processor")
    ET.SubElement(processor, "entry", name="model", type="string").text = info.get("Chip", "Unknown")
    
    # Add disk section
    disk_entry = ET.SubElement(hardware_report, "disk")
    
    # Set disk model - ensure it's not the Mac model
    if disk_model == mac_model:
        # If disk_model is same as Mac model, use a fallback
        ET.SubElement(disk_entry, "entry", name="model", type="string").text = "Apple SSD"
    else:
        ET.SubElement(disk_entry, "entry", name="model", type="string").text = disk_model
    
    # Get disk serial number (use volume UUID as fallback)
    disk_serial = "Unknown"
    try:
        # Try to get Device UUID first (most reliable for internal disks)
        device_uuid = subprocess.check_output(f"diskutil info {disk_bsd_name} | grep 'Device / Media UUID' | awk '{{print $NF}}'", shell=True, text=True).strip()
        if device_uuid and device_uuid != "":
            disk_serial = device_uuid
        else:
            # Try to get Disk UUID next
            disk_uuid = subprocess.check_output(f"diskutil info {disk_bsd_name} | grep 'Disk / Partition UUID' | awk '{{print $NF}}'", shell=True, text=True).strip()
            if disk_uuid and disk_uuid != "":
                disk_serial = disk_uuid
            else:
                # Finally try Volume UUID
                volume_uuid = subprocess.check_output(f"diskutil info {disk_bsd_name}s1 | grep 'Volume UUID' | awk '{{print $NF}}'", shell=True, text=True).strip()
                if volume_uuid and volume_uuid != "":
                    disk_serial = volume_uuid
    except:
        pass
    
    ET.SubElement(disk_entry, "entry", name="serial", type="string").text = disk_serial
    
    # Determine disk vendor from model
    vendor = "Apple"
    if "SAMSUNG" in disk_model.upper():
        vendor = "Samsung"
    elif "TOSHIBA" in disk_model.upper():
        vendor = "Toshiba"
    elif "HITACHI" in disk_model.upper():
        vendor = "Hitachi"
    elif "SEAGATE" in disk_model.upper():
        vendor = "Seagate"
    elif "INTEL" in disk_model.upper():
        vendor = "Intel"
    
    ET.SubElement(disk_entry, "entry", name="vendor", type="string").text = vendor
    
    # Determine interface type
    interface_type = "NVMe"
    try:
        disk_info = subprocess.check_output(f"diskutil info {disk_bsd_name}", shell=True, text=True)
        if "NVMe" in disk_info:
            interface_type = "NVMe"
        elif "SATA" in disk_info:
            interface_type = "SATA"
        elif "PCI" in disk_info:
            interface_type = "PCI"
    except:
        pass
    
    ET.SubElement(disk_entry, "entry", name="interface_type", type="string").text = interface_type
    
    # Get storage capacity
    storage_str = info.get("SSD Storage", "512GB")
    
    # Convert storage to bytes (approximate)
    try:
        if not storage_str or storage_str.strip() == "":
            # Default to 512GB if storage_str is empty
            capacity = 512 * 1000000000
        elif "TB" in storage_str:
            capacity = int(float(storage_str.replace("TB", "").strip()) * 1000000000000)
        else:
            capacity = int(float(storage_str.replace("GB", "").strip()) * 1000000000)
    except ValueError:
        # Fallback to 512GB if conversion fails
        capacity = 512 * 1000000000
    
    ET.SubElement(disk_entry, "entry", name="capacity", type="uint").text = str(capacity)
    
    # Convert XML to string
    xml_str = ET.tostring(report, encoding="utf-8").decode()
    
    # Print results
    print("\nHardware Info:")
    for key, value in info.items():
        print(f"{key}: {value}")
    
    print("\nInternal Disk Info:")
    print(f"Disk Model: {disk_model}")
    print(f"Disk Serial: {disk_serial}")
    print(f"Interface Type: {interface_type}")
    print(f"Storage: {storage_str}")
    
    print("\nXML Output:")
    print(xml_str)
    
    # Create a formatted output string
    output_str = f"Disk: {disk_model}, Serial: {disk_serial}, {storage_str}, {interface_type}"
    print("\nOutput String:")
    print(output_str)
    
    return True

if __name__ == "__main__":
    print("Testing XML generation with fixed code...")
    success = test_xml_generation()
    if success:
        print("\nXML generation test completed successfully!")
    else:
        print("\nXML generation test failed!")