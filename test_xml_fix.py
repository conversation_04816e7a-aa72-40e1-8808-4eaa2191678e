#!/usr/bin/env python3

import subprocess
import xml.etree.ElementTree as ET
import xml.dom.minidom
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def get_internal_disk_info():
    """Get information about the internal Apple disk specifically"""
    try:
        # Try multiple system_profiler data types to cover all disk types (SATA, NVMe, PCIe)
        disk_info = {}
        
        # First try SPNVMeDataType for NVMe drives (common in newer Macs)
        try:
            nvme_output = subprocess.check_output("system_profiler SPNVMeDataType", shell=True, text=True)
            if 'Model:' in nvme_output:
                # Parse NVMe output
                lines = nvme_output.split('\n')
                current_disk = {}
                
                for line in lines:
                    line = line.strip()
                    if line.endswith(':') and not line.startswith('NVMe'):
                        # Start of a new disk section
                        current_disk = {'name': line.rstrip(':')}
                    elif 'Model:' in line and current_disk:
                        current_disk['model'] = line.split(':', 1)[1].strip()
                        # Found a model, this is likely the internal disk
                        return {
                            'model': current_disk['model'],
                            'bsd_name': '/dev/disk0',  # Typical for boot disk
                            'is_internal': True
                        }
        except Exception as e:
            logging.info(f"NVMe check failed: {e}")
            pass
            
        # Then try SPSerialATADataType for SATA drives (older Macs)
        try:
            sata_output = subprocess.check_output("system_profiler SPSerialATADataType", shell=True, text=True)
            
            # Look for Apple SSD with Bay Name (indicates internal)
            lines = sata_output.split('\n')
            current_disk = {}
            internal_disk = None

            for line in lines:
                line = line.strip()
                if line.endswith(':') and ('APPLE' in line or 'SSD' in line):
                    # Start of a new disk section
                    current_disk = {'name': line.rstrip(':')}
                elif 'Model:' in line and current_disk:
                    current_disk['model'] = line.split(':', 1)[1].strip()
                elif 'BSD Name:' in line and current_disk:
                    current_disk['bsd_name'] = line.split(':', 1)[1].strip()
                elif 'Bay Name:' in line and current_disk:
                    current_disk['bay_name'] = line.split(':', 1)[1].strip()
                    # If it has a Bay Name, it's likely internal
                    if 'APPLE' in current_disk.get('model', '').upper():
                        internal_disk = current_disk
                        break

            if internal_disk:
                return {
                    'model': internal_disk.get('model', 'Unknown'),
                    'bsd_name': internal_disk.get('bsd_name', '/dev/disk0'),
                    'is_internal': True
                }
            else:
                # Fallback: look for any Apple SSD
                for line in lines:
                    if 'Model:' in line and 'APPLE' in line.upper():
                        model = line.split(':', 1)[1].strip()
                        return {
                            'model': model,
                            'bsd_name': '/dev/disk1',  # Common for internal Apple SSD
                            'is_internal': True
                        }
        except Exception as e:
            logging.info(f"SATA check failed: {e}")
            pass
            
        # Finally, try SPStorageDataType as a last resort (most generic)
        try:
            storage_output = subprocess.check_output("system_profiler SPStorageDataType", shell=True, text=True)
            lines = storage_output.split('\n')
            
            for i, line in enumerate(lines):
                if 'Device Name:' in line:
                    model = line.split(':', 1)[1].strip()
                    # Skip external drives or disk images
                    if 'disk image' not in model.lower() and 'external' not in model.lower():
                        return {
                            'model': model,
                            'bsd_name': '/dev/disk0',
                            'is_internal': True
                        }
        except Exception as e:
            logging.info(f"Storage check failed: {e}")
            pass

        # Final fallback: use diskutil to get info about disk0 (usually the boot disk)
        try:
            diskutil_output = subprocess.check_output("diskutil info /dev/disk0 | grep 'Device / Media Name'", shell=True, text=True)
            if diskutil_output:
                model = diskutil_output.split(':', 1)[1].strip()
                return {
                    'model': model,
                    'bsd_name': '/dev/disk0',
                    'is_internal': True
                }
        except Exception as e:
            logging.info(f"Diskutil check failed: {e}")
            pass
            
        # Ultimate fallback
        return {
            'model': 'Unknown Internal Disk',
            'bsd_name': '/dev/disk0',
            'is_internal': False
        }

    except Exception as e:
        logging.error(f"Error getting internal disk info: {e}")
        return {
            'model': 'Unknown Internal Disk',
            'bsd_name': '/dev/disk0',
            'is_internal': False
        }

def get_hardware_info():
    commands = {
        "Model Identifier": "sysctl -n hw.model",
        "Chip": "sysctl -n machdep.cpu.brand_string",
        "Memory": "sysctl -n hw.memsize | awk '{ printf \"%.0f GB\", $0/1024/1024/1024 }'",
        # SSD Storage: Get all internal disk sizes (supports multiple drives)
        "SSD Storage": "diskutil list | grep 'GUID_partition_scheme' | grep '\\*.*GB.*disk[0-9]' | sed 's/.*\\*\\([0-9.]*\\) GB.*/\\1 GB/' | paste -sd ', ' -",
        # Disk Model: Get all internal disk models using SPStorageDataType (more reliable)
        "Disk Model": "system_profiler SPStorageDataType | grep 'Device Name:' | grep -v 'Disk Image' | awk -F':' '{gsub(/^[ \\t]+|[ \\t]+$/, \"\", $2); print $2}' | paste -sd ', ' -",
        "Serial number": "ioreg -c IOPlatformExpertDevice -d 2 | grep -i 'IOPlatformSerialNumber' | awk -F \"\\\"\" '{print $4}'",
        "macOS Version": "sw_vers -productVersion"
    }
    info = {}
    for key, command in commands.items():
        try:
            output = subprocess.check_output(command, shell=True, text=True).strip()
            info[key] = output.strip('"')
            logging.info(f"{key}: {info[key]}")
        except subprocess.CalledProcessError:
            info[key] = "Unable to retrieve"
    return info

def test_xml_generation():
    # Get hardware info
    info = get_hardware_info()
    
    # Get internal disk info
    internal_disk = get_internal_disk_info()
    logging.info(f"Internal disk info: {internal_disk}")
    
    # Create XML structure
    root = ET.Element("root")
    report = ET.SubElement(root, "report")
    hardware_report = ET.SubElement(report, "hardware_report")
    
    # Add disk information
    disks_entries = ET.SubElement(hardware_report, "entries", name="disks")
    disk_entry = ET.SubElement(disks_entries, "entries", name="disk")
    ET.SubElement(disk_entry, "entry", name="id", type="uint").text = "34"
    ET.SubElement(disk_entry, "entry", name="index", type="uint").text = "1"
    
    # Get the disk model from internal_disk first (most accurate)
    disk_model = internal_disk['model']
    
    # Make sure we're not using the Mac model identifier
    mac_model = info.get("Model Identifier", "")
    logging.info(f"Mac model: {mac_model}")
    logging.info(f"Initial disk model: {disk_model}")
    
    if disk_model == mac_model or disk_model == "Unknown Internal Disk":
        # Try to get disk model from SPStorageDataType
        all_disk_models = info.get("Disk Model", "Unknown")
        logging.info(f"All disk models: {all_disk_models}")
        if all_disk_models != "Unknown" and all_disk_models != "Unable to retrieve":
            # If multiple disk models, prefer Apple ones
            if "," in all_disk_models:
                models = all_disk_models.split(',')
                # First try to find an Apple disk
                for model in models:
                    if "APPLE" in model.strip().upper():
                        disk_model = model.strip()
                        logging.info(f"Found Apple disk model: {disk_model}")
                        break
                # If no Apple disk found, use the first one
                if disk_model == mac_model or disk_model == "Unknown Internal Disk":
                    disk_model = models[0].strip()
                    logging.info(f"Using first disk model: {disk_model}")
            else:
                disk_model = all_disk_models.strip()
                logging.info(f"Using single disk model: {disk_model}")
    
    # Final check to ensure we're not using Mac model
    if disk_model == mac_model:
        # Last resort: try to get disk info directly
        try:
            disk_info = subprocess.check_output("diskutil info /dev/disk0 | grep 'Device / Media Name'", shell=True, text=True)
            if disk_info:
                disk_model = disk_info.split(':', 1)[1].strip()
                logging.info(f"Using diskutil disk model: {disk_model}")
        except Exception as e:
            logging.error(f"Diskutil error: {e}")
            # If all else fails, use a generic name
            disk_model = "Internal Storage Device"
            logging.info(f"Using generic disk model: {disk_model}")
    
    ET.SubElement(disk_entry, "entry", name="model", type="string").text = disk_model
    
    # Get disk serial
    disk_serial = info.get("Serial number", "Unknown")
    try:
        # Try multiple methods to get disk-specific serial
        # First try to get disk UUID (most reliable)
        disk_serial_cmd = "diskutil info /dev/disk0 | grep 'Device / Media UUID' | awk -F':' '{print $2}' | xargs"
        disk_serial_output = subprocess.check_output(disk_serial_cmd, shell=True, text=True).strip()
        if disk_serial_output and disk_serial_output != "Unknown":
            disk_serial = disk_serial_output
            logging.info(f"Using disk UUID: {disk_serial}")
        else:
            # Try to get disk serial number
            disk_serial_cmd = "diskutil info /dev/disk0 | grep 'Disk / Partition UUID' | awk -F':' '{print $2}' | xargs"
            disk_serial_output = subprocess.check_output(disk_serial_cmd, shell=True, text=True).strip()
            if disk_serial_output and disk_serial_output != "Unknown":
                disk_serial = disk_serial_output
                logging.info(f"Using disk partition UUID: {disk_serial}")
            else:
                # Try to get volume UUID as last resort
                disk_serial_cmd = "diskutil info / | grep 'Volume UUID' | awk -F':' '{print $2}' | xargs"
                disk_serial_output = subprocess.check_output(disk_serial_cmd, shell=True, text=True).strip()
                if disk_serial_output and disk_serial_output != "Unknown":
                    disk_serial = disk_serial_output
                    logging.info(f"Using volume UUID: {disk_serial}")
    except Exception as e:
        logging.error(f"Could not get disk serial: {e}")
        # Keep using system serial as fallback
        logging.info(f"Using system serial as fallback: {disk_serial}")
    
    ET.SubElement(disk_entry, "entry", name="serial", type="string").text = disk_serial
    
    # Convert to XML string with proper formatting
    xml_str = ET.tostring(root, encoding='utf-8')
    # Parse and prettify the XML
    dom = xml.dom.minidom.parseString(xml_str)
    pretty_xml = dom.toprettyxml(indent="  ")
    # Remove empty lines and fix formatting
    lines = [line for line in pretty_xml.split('\n') if line.strip()]
    formatted_xml = '\n'.join(lines)
    
    # Print the XML
    print("\nGenerated XML:")
    print(formatted_xml)
    
    # Print the disk information in the format mentioned in the issue
    print(f"\nDisk information format:")
    storage_str = info.get("SSD Storage", "1000GB")
    interface_type = "PCI"
    try:
        disk_info = subprocess.check_output("diskutil info / | grep 'Protocol'", shell=True, text=True).strip()
        if "NVMe" in disk_info:
            interface_type = "NVMe"
        elif "SATA" in disk_info:
            interface_type = "SATA"
        elif "PCI" in disk_info:
            interface_type = "PCI"
    except:
        pass
    
    print(f"Disk: 1{disk_model}, Serial: {disk_serial}, {storage_str}, {interface_type}")
    print(f"Mac Model: {mac_model}")

if __name__ == "__main__":
    test_xml_generation()