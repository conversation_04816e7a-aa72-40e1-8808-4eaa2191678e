# created by tools/loadICU.tcl -- do not edit
namespace eval ::tcl::clock {
    ::msgcat::mcset zh DAYS_OF_WEEK_ABBREV [list \
        "星期日"\
        "星期一"\
        "星期二"\
        "星期三"\
        "星期四"\
        "星期五"\
        "星期六"]
    ::msgcat::mcset zh DAYS_OF_WEEK_FULL [list \
        "星期日"\
        "星期一"\
        "星期二"\
        "星期三"\
        "星期四"\
        "星期五"\
        "星期六"]
    ::msgcat::mcset zh MONTHS_ABBREV [list \
        "一月"\
        "二月"\
        "三月"\
        "四月"\
        "五月"\
        "六月"\
        "七月"\
        "八月"\
        "九月"\
        "十月"\
        "十一月"\
        "十二月"\
        ""]
    ::msgcat::mcset zh MONTHS_FULL [list \
        "一月"\
        "二月"\
        "三月"\
        "四月"\
        "五月"\
        "六月"\
        "七月"\
        "八月"\
        "九月"\
        "十月"\
        "十一月"\
        "十二月"\
        ""]
    ::msgcat::mcset zh BCE "公元前"
    ::msgcat::mcset zh CE "公元"
    ::msgcat::mcset zh AM "上午"
    ::msgcat::mcset zh PM "下午"
    ::msgcat::mcset zh LOCALE_NUMERALS "〇 一 二 三 四 五 六 七 八 九 十 十一 十二 十三 十四 十五 十六 十七 十八 十九 二十 廿一 廿二 廿三 廿四 廿五 廿六 廿七 廿八 廿九 三十 卅一 卅二 卅三 卅四 卅五 卅六 卅七 卅八 卅九 四十 四十一 四十二 四十三 四十四 四十五 四十六 四十七 四十八 四十九 五十 五十一 五十二 五十三 五十四 五十五 五十六 五十七 五十八 五十九 六十 六十一 六十二 六十三 六十四 六十五 六十六 六十七 六十八 六十九 七十 七十一 七十二 七十三 七十四 七十五 七十六 七十七 七十八 七十九 八十 八十一 八十二 八十三 八十四 八十五 八十六 八十七 八十八 八十九 九十 九十一 九十二 九十三 九十四 九十五 九十六 九十七 九十八 九十九"
    ::msgcat::mcset zh LOCALE_DATE_FORMAT "公元%Y年%B%Od日"
    ::msgcat::mcset zh LOCALE_TIME_FORMAT "%OH时%OM分%OS秒"
    ::msgcat::mcset zh LOCALE_DATE_TIME_FORMAT "%A %Y年%B%Od日%OH时%OM分%OS秒 %z"
}
