# created by tools/loadICU.tcl -- do not edit
namespace eval ::tcl::clock {
    ::msgcat::mcset lt DAYS_OF_WEEK_ABBREV [list \
        "Sk"\
        "Pr"\
        "An"\
        "Tr"\
        "Kt"\
        "Pn"\
        "Št"]
    ::msgcat::mcset lt DAYS_OF_WEEK_FULL [list \
        "Sekmadien<PERSON>"\
        "Pirmadienis"\
        "Antradienis"\
        "Trečiadienis"\
        "Ketvirtadien<PERSON>"\
        "Penktadienis"\
        "Šeštadien<PERSON>"]
    ::msgcat::mcset lt MONTHS_ABBREV [list \
        "Sau"\
        "Vas"\
        "Kov"\
        "Bal"\
        "Geg"\
        "Bir"\
        "Lie"\
        "Rgp"\
        "Rgs"\
        "Spa"\
        "Lap"\
        "Grd"\
        ""]
    ::msgcat::mcset lt MONTHS_FULL [list \
        "Sausio"\
        "Vasario"\
        "<PERSON><PERSON>"\
        "<PERSON>land<PERSON><PERSON>"\
        "<PERSON>eg<PERSON><PERSON><PERSON><PERSON>"\
        "<PERSON>ir<PERSON>elio"\
        "Liepos"\
        "Rugpjū<PERSON><PERSON>"\
        "<PERSON><PERSON><PERSON><PERSON>"\
        "Spalio"\
        "Lapkrič<PERSON>"\
        "Gruodž<PERSON>"\
        ""]
    ::msgcat::mcset lt BCE "pr.Kr."
    ::msgcat::mcset lt CE "po.Kr."
    ::msgcat::mcset lt DATE_FORMAT "%Y.%m.%e"
    ::msgcat::mcset lt TIME_FORMAT "%H.%M.%S"
    ::msgcat::mcset lt DATE_TIME_FORMAT "%Y.%m.%e %H.%M.%S %z"
}
