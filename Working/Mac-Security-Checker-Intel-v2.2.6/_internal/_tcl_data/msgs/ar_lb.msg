# created by tools/loadICU.tcl -- do not edit
namespace eval ::tcl::clock {
    ::msgcat::mcset ar_LB DAYS_OF_WEEK_ABBREV [list \
        "الأحد"\
        "الاثنين"\
        "الثلاثاء"\
        "الأربعاء"\
        "الخميس"\
        "الجمعة"\
        "السبت"]
    ::msgcat::mcset ar_LB MONTHS_ABBREV [list \
        "كانون الثاني"\
        "شباط"\
        "آذار"\
        "نيسان"\
        "نوار"\
        "حزيران"\
        "تموز"\
        "آب"\
        "أيلول"\
        "تشرين الأول"\
        "تشرين الثاني"\
        "كانون الأول"\
        ""]
    ::msgcat::mcset ar_LB MONTHS_FULL [list \
        "كانون الثاني"\
        "شباط"\
        "آذار"\
        "نيسان"\
        "نوار"\
        "حزيران"\
        "تموز"\
        "آب"\
        "أيلول"\
        "تشرين الأول"\
        "تشرين الثاني"\
        "كانون الأول"\
        ""]
}
