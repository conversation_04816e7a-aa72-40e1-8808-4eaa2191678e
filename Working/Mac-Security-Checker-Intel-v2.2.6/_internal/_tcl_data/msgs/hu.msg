# created by tools/loadICU.tcl -- do not edit
namespace eval ::tcl::clock {
    ::msgcat::mcset hu DAYS_OF_WEEK_ABBREV [list \
        "V"\
        "H"\
        "K"\
        "Sze"\
        "Cs"\
        "P"\
        "Szo"]
    ::msgcat::mcset hu DAYS_OF_WEEK_FULL [list \
        "vasárnap"\
        "hétfő"\
        "kedd"\
        "szerda"\
        "csütörtök"\
        "péntek"\
        "szombat"]
    ::msgcat::mcset hu MONTHS_ABBREV [list \
        "jan."\
        "febr."\
        "márc."\
        "ápr."\
        "máj."\
        "jún."\
        "júl."\
        "aug."\
        "szept."\
        "okt."\
        "nov."\
        "dec."\
        ""]
    ::msgcat::mcset hu MONTHS_FULL [list \
        "január"\
        "február"\
        "március"\
        "április"\
        "május"\
        "június"\
        "július"\
        "augusztus"\
        "szeptember"\
        "október"\
        "november"\
        "december"\
        ""]
    ::msgcat::mcset hu BCE "i.e."
    ::msgcat::mcset hu CE "i.u."
    ::msgcat::mcset hu AM "DE"
    ::msgcat::mcset hu PM "DU"
    ::msgcat::mcset hu DATE_FORMAT "%Y.%m.%d."
    ::msgcat::mcset hu TIME_FORMAT "%k:%M:%S"
    ::msgcat::mcset hu DATE_TIME_FORMAT "%Y.%m.%d. %k:%M:%S %z"
}
