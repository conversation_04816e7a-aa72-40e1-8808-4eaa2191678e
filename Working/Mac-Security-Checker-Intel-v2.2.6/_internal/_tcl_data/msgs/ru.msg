# created by tools/loadICU.tcl -- do not edit
namespace eval ::tcl::clock {
    ::msgcat::mcset ru DAYS_OF_WEEK_ABBREV [list \
        "Вс"\
        "Пн"\
        "Вт"\
        "Ср"\
        "Чт"\
        "Пт"\
        "Сб"]
    ::msgcat::mcset ru DAYS_OF_WEEK_FULL [list \
        "воскресенье"\
        "понедельник"\
        "вторник"\
        "среда"\
        "четверг"\
        "пятница"\
        "суббота"]
    ::msgcat::mcset ru MONTHS_ABBREV [list \
        "янв"\
        "фев"\
        "мар"\
        "апр"\
        "май"\
        "июн"\
        "июл"\
        "авг"\
        "сен"\
        "окт"\
        "ноя"\
        "дек"\
        ""]
    ::msgcat::mcset ru MONTHS_FULL [list \
        "Январь"\
        "Февраль"\
        "Март"\
        "Апрель"\
        "Май"\
        "Июнь"\
        "Июль"\
        "Август"\
        "Сентябрь"\
        "Октябрь"\
        "Ноябрь"\
        "Декабрь"\
        ""]
    ::msgcat::mcset ru BCE "до н.э."
    ::msgcat::mcset ru CE "н.э."
    ::msgcat::mcset ru DATE_FORMAT "%d.%m.%Y"
    ::msgcat::mcset ru TIME_FORMAT "%k:%M:%S"
    ::msgcat::mcset ru DATE_TIME_FORMAT "%d.%m.%Y %k:%M:%S %z"
}
