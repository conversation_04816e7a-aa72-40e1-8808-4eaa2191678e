# created by tools/loadICU.tcl -- do not edit
namespace eval ::tcl::clock {
    ::msgcat::mcset gl DAYS_OF_WEEK_ABBREV [list \
        "Dom"\
        "Lun"\
        "Mar"\
        "<PERSON><PERSON><PERSON>"\
        "Xov"\
        "Ven"\
        "Sáb"]
    ::msgcat::mcset gl DAYS_OF_WEEK_FULL [list \
        "Domingo"\
        "Luns"\
        "Mart<PERSON>"\
        "Mércores"\
        "Xoves"\
        "Venres"\
        "Sábado"]
    ::msgcat::mcset gl MONTHS_ABBREV [list \
        "Xan"\
        "Feb"\
        "Mar"\
        "Abr"\
        "Mai"\
        "Xu<PERSON>"\
        "Xul"\
        "Ago"\
        "Set"\
        "Out"\
        "Nov"\
        "Dec"\
        ""]
    ::msgcat::mcset gl MONTHS_FULL [list \
        "Xaneiro"\
        "Febreiro"\
        "Marzo"\
        "Abril"\
        "Maio"\
        "<PERSON><PERSON>"\
        "<PERSON><PERSON>"\
        "Agosto"\
        "Setembro"\
        "Outubro"\
        "Novembro"\
        "Decembro"\
        ""]
}
