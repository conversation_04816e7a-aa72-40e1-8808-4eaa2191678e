<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Resources/PIL/AvifImagePlugin.py</key>
		<data>
		XypEcnH36Hznw+0x+BqbOjQPYD8=
		</data>
		<key>Resources/PIL/BdfFontFile.py</key>
		<data>
		34X90VlSRLJbv19v+0dMxMK1uwE=
		</data>
		<key>Resources/PIL/BlpImagePlugin.py</key>
		<data>
		SALk5dKTs7ivauUwvif1by9v0xs=
		</data>
		<key>Resources/PIL/BmpImagePlugin.py</key>
		<data>
		W5nmc+ZETy30fZfk4yn9huzbde4=
		</data>
		<key>Resources/PIL/BufrStubImagePlugin.py</key>
		<data>
		nD8/iLuelX5DexB5M28SONb2qyc=
		</data>
		<key>Resources/PIL/ContainerIO.py</key>
		<data>
		FAatcHDYl4Xn8a6maEwhOuLhD3Q=
		</data>
		<key>Resources/PIL/CurImagePlugin.py</key>
		<data>
		PfPxHSuXl5NLZU37L6zyk7W97Q0=
		</data>
		<key>Resources/PIL/DcxImagePlugin.py</key>
		<data>
		CrtkoAcwWW/X8/QMs6XC1NGTITU=
		</data>
		<key>Resources/PIL/DdsImagePlugin.py</key>
		<data>
		7wIkKT+PZvcgevFvtWj/Z75qW5k=
		</data>
		<key>Resources/PIL/EpsImagePlugin.py</key>
		<data>
		zKnnKzVGhKO9dFqp3vfDuTHXGUU=
		</data>
		<key>Resources/PIL/ExifTags.py</key>
		<data>
		aUR2fPb0L5j+A7w2rbIIxSrJCpw=
		</data>
		<key>Resources/PIL/FitsImagePlugin.py</key>
		<data>
		d1EttNGDdF+8EQq8ZVbnpvEP4UU=
		</data>
		<key>Resources/PIL/FliImagePlugin.py</key>
		<data>
		vlWgFKe1P3IFJ3jBLcktDLhgyhk=
		</data>
		<key>Resources/PIL/FontFile.py</key>
		<data>
		3cuhcOEHO6gegisGASpWeATm/1g=
		</data>
		<key>Resources/PIL/FpxImagePlugin.py</key>
		<data>
		pP06z20lTftbMRX7SYg5zsR6CFQ=
		</data>
		<key>Resources/PIL/FtexImagePlugin.py</key>
		<data>
		OdcdJ+rfBT9iTBJsgDDz8ubXqlU=
		</data>
		<key>Resources/PIL/GbrImagePlugin.py</key>
		<data>
		19q6ACaPrNc0DbT1RaENNRcS8Yk=
		</data>
		<key>Resources/PIL/GdImageFile.py</key>
		<data>
		4Wh7adBlqtinNFfwkabkTH2hS7g=
		</data>
		<key>Resources/PIL/GifImagePlugin.py</key>
		<data>
		eBHfSp5i1g9OgfBLnZjE+cuy2K8=
		</data>
		<key>Resources/PIL/GimpGradientFile.py</key>
		<data>
		p61eYlYc1uSiKAVlPFn50T7HWUg=
		</data>
		<key>Resources/PIL/GimpPaletteFile.py</key>
		<data>
		KdUZGEviU5vW1j/i5ebuQTPfS8s=
		</data>
		<key>Resources/PIL/GribStubImagePlugin.py</key>
		<data>
		OhliEz7G5E7XRvbNWXXhnMHwIWw=
		</data>
		<key>Resources/PIL/Hdf5StubImagePlugin.py</key>
		<data>
		eHaiZbqSjj2pHQ1tlIPClbl2YUU=
		</data>
		<key>Resources/PIL/IcnsImagePlugin.py</key>
		<data>
		c2z2BeI5YtwSNIW+/HZ9nb8Y9VQ=
		</data>
		<key>Resources/PIL/IcoImagePlugin.py</key>
		<data>
		5dpZosnfYCz0JdTNL0G924E7jHk=
		</data>
		<key>Resources/PIL/ImImagePlugin.py</key>
		<data>
		HAmyL7m2Azvm/9Fu7PDdIfoDO0s=
		</data>
		<key>Resources/PIL/Image.py</key>
		<data>
		CdmrqUehS3jvK0NYNulmSAGApbs=
		</data>
		<key>Resources/PIL/ImageChops.py</key>
		<data>
		VUtAxP8cwP7WoGKh6hqL09r9F1s=
		</data>
		<key>Resources/PIL/ImageCms.py</key>
		<data>
		aePpaNEtBMxGt5m4IKc2W47cqc8=
		</data>
		<key>Resources/PIL/ImageColor.py</key>
		<data>
		7XjxfBRR7QGt0+Pa/vUzw3PIi7A=
		</data>
		<key>Resources/PIL/ImageDraw.py</key>
		<data>
		a94t582aObPjUf/QkLKKEs1B7jE=
		</data>
		<key>Resources/PIL/ImageDraw2.py</key>
		<data>
		/UauigPiUhG0yTX47gkxkX1zCOk=
		</data>
		<key>Resources/PIL/ImageEnhance.py</key>
		<data>
		8l9lqtuRXl5yn8etEtKVZXI7Tsg=
		</data>
		<key>Resources/PIL/ImageFile.py</key>
		<data>
		tLMNBkc6K8TJ29QofyrobNzC7HI=
		</data>
		<key>Resources/PIL/ImageFilter.py</key>
		<data>
		Qww1aVPV3CrosMeM3H5trxTgcZw=
		</data>
		<key>Resources/PIL/ImageFont.py</key>
		<data>
		Wh1tYhoLJ19p+Y3a/LOYDAS6EMk=
		</data>
		<key>Resources/PIL/ImageGrab.py</key>
		<data>
		9UcoM/GRyd8oLuPWcS9k0YI/FGA=
		</data>
		<key>Resources/PIL/ImageMath.py</key>
		<data>
		g3F3z+bsAqbxo80tBOBnUB6Rc8E=
		</data>
		<key>Resources/PIL/ImageMode.py</key>
		<data>
		hIGWpXy1gm6PhIXPDVmrVo5XZJ8=
		</data>
		<key>Resources/PIL/ImageMorph.py</key>
		<data>
		Ra6gHuCvVNkq5mPZaf6XSP01LzY=
		</data>
		<key>Resources/PIL/ImageOps.py</key>
		<data>
		S8xmfSIATF4rXXq7xs/E4alnLXk=
		</data>
		<key>Resources/PIL/ImagePalette.py</key>
		<data>
		lhc3M973I+ZIMhWx9dpBurN6YYo=
		</data>
		<key>Resources/PIL/ImagePath.py</key>
		<data>
		nbqX54XTJKzq5x63svEbrsp9aMc=
		</data>
		<key>Resources/PIL/ImageQt.py</key>
		<data>
		rFVQqufGJLGHX7sMat3bVEVZR80=
		</data>
		<key>Resources/PIL/ImageSequence.py</key>
		<data>
		GkRBTI2xfOUmq9YAKdIVdAv+Tnw=
		</data>
		<key>Resources/PIL/ImageShow.py</key>
		<data>
		z1PAkdHk3BFTPOuNQ9JSwmwgtQk=
		</data>
		<key>Resources/PIL/ImageStat.py</key>
		<data>
		K5rcNhvB+6IW4fRlA1a5XnZKZds=
		</data>
		<key>Resources/PIL/ImageTk.py</key>
		<data>
		1dGQ1gsW30rgxmPOdNMg74ovNcY=
		</data>
		<key>Resources/PIL/ImageTransform.py</key>
		<data>
		+5ZxzhS4DOmWAzDs9By/GZKZC+g=
		</data>
		<key>Resources/PIL/ImageWin.py</key>
		<data>
		g0Ie7ppsrN894nl3I3foqsw2MN8=
		</data>
		<key>Resources/PIL/ImtImagePlugin.py</key>
		<data>
		ygXXr2oMWM0s3Ny9yxTh2hhliog=
		</data>
		<key>Resources/PIL/IptcImagePlugin.py</key>
		<data>
		uyYCbtU/bXoOg2PrD+NiYSOMZj0=
		</data>
		<key>Resources/PIL/Jpeg2KImagePlugin.py</key>
		<data>
		vUSHt+qpGlc39QClT+Iq5txyBl0=
		</data>
		<key>Resources/PIL/JpegImagePlugin.py</key>
		<data>
		wgyl4twWqvTJ30IhKeWTxyLev4A=
		</data>
		<key>Resources/PIL/JpegPresets.py</key>
		<data>
		9TDwQ5UBcqe8X9gstYq31Yw7Q2g=
		</data>
		<key>Resources/PIL/McIdasImagePlugin.py</key>
		<data>
		7+ZKwLV6469EdAaTnxxaoBZw96A=
		</data>
		<key>Resources/PIL/MicImagePlugin.py</key>
		<data>
		aF9iq77UR/4A/1va7ZOu8p+hlIY=
		</data>
		<key>Resources/PIL/MpegImagePlugin.py</key>
		<data>
		he4yw1wvpP7hAD0QLEYTTOez41Q=
		</data>
		<key>Resources/PIL/MpoImagePlugin.py</key>
		<data>
		mAZZxJJ7GH1llvkwRaQ6KQuPCgg=
		</data>
		<key>Resources/PIL/MspImagePlugin.py</key>
		<data>
		5lATBZT2sfPfQeyH8mRBpBWIV1w=
		</data>
		<key>Resources/PIL/PSDraw.py</key>
		<data>
		1Z/J+bJsj6F7Iy9/CKk78OXXGUM=
		</data>
		<key>Resources/PIL/PaletteFile.py</key>
		<data>
		5+85PiLpJVSz6ysUGvjwwAobOrY=
		</data>
		<key>Resources/PIL/PalmImagePlugin.py</key>
		<data>
		bHCLS9NVvaZLa44WbDCiWvy3Ux0=
		</data>
		<key>Resources/PIL/PcdImagePlugin.py</key>
		<data>
		rvP1lggpQpdT0yYidPBx/xVtXdU=
		</data>
		<key>Resources/PIL/PcfFontFile.py</key>
		<data>
		QJWlI4Iq/SGZz/UxK75zROcLjQU=
		</data>
		<key>Resources/PIL/PcxImagePlugin.py</key>
		<data>
		7vFq+8VLaCF0RGEXsdVwgTikG/8=
		</data>
		<key>Resources/PIL/PdfImagePlugin.py</key>
		<data>
		UA08B636kLCkrbSNH4pPRLY4scU=
		</data>
		<key>Resources/PIL/PdfParser.py</key>
		<data>
		fpETOhsScDq7SAd1+jzdS55qGoE=
		</data>
		<key>Resources/PIL/PixarImagePlugin.py</key>
		<data>
		xr/MNbeoTa+LOF31RUGo0D2Yfmg=
		</data>
		<key>Resources/PIL/PngImagePlugin.py</key>
		<data>
		qK5a40gM1Vyt943Xsliqg23oZto=
		</data>
		<key>Resources/PIL/PpmImagePlugin.py</key>
		<data>
		vxt9vDvLphuN677zKf6Avktgf18=
		</data>
		<key>Resources/PIL/PsdImagePlugin.py</key>
		<data>
		H/7j0+2fjIwhR4ey8oNEMqx7D1s=
		</data>
		<key>Resources/PIL/QoiImagePlugin.py</key>
		<data>
		LnjYyHsqaGe3wYp/Dy74JEAalGY=
		</data>
		<key>Resources/PIL/SgiImagePlugin.py</key>
		<data>
		2mtzI41XeMBHR+bEIHfSl93aDmg=
		</data>
		<key>Resources/PIL/SpiderImagePlugin.py</key>
		<data>
		D83Oiwt3h1X0b2FM+lrSPlvDZuI=
		</data>
		<key>Resources/PIL/SunImagePlugin.py</key>
		<data>
		T56qhS0i6Bo4m2IjFQ+xbKDDQXU=
		</data>
		<key>Resources/PIL/TarIO.py</key>
		<data>
		Clv5sk8SyI/6wrlNfzXFBwTXcwk=
		</data>
		<key>Resources/PIL/TgaImagePlugin.py</key>
		<data>
		AIuwCE6UtjS/glXSeQibyUUh3ak=
		</data>
		<key>Resources/PIL/TiffImagePlugin.py</key>
		<data>
		I+X8/7GAeMF80CEls912vr7rFM0=
		</data>
		<key>Resources/PIL/TiffTags.py</key>
		<data>
		2Z/yFrVgUGpEYH4iWdAuoC4UYeY=
		</data>
		<key>Resources/PIL/WalImageFile.py</key>
		<data>
		8KgHbI3o4rAO2X+ESDQRiUDmMG4=
		</data>
		<key>Resources/PIL/WebPImagePlugin.py</key>
		<data>
		03HPkR5DVAc4bsjHtN2TfMSE0wg=
		</data>
		<key>Resources/PIL/WmfImagePlugin.py</key>
		<data>
		obOW36v22A9+QHDhW+Psbhk64nA=
		</data>
		<key>Resources/PIL/XVThumbImagePlugin.py</key>
		<data>
		fxXkY9c4J1DxZ74DokK7rwbAUfY=
		</data>
		<key>Resources/PIL/XbmImagePlugin.py</key>
		<data>
		bb7d7BPT/eNEZKv8uMlY2qS6w/E=
		</data>
		<key>Resources/PIL/XpmImagePlugin.py</key>
		<data>
		66sYamxbH36aOdYtIAI4Cd5VMos=
		</data>
		<key>Resources/PIL/__init__.py</key>
		<data>
		49BjUkryCH/NU9XCIJGT0QQQtJw=
		</data>
		<key>Resources/PIL/__main__.py</key>
		<data>
		RZ7iCPhrB+IkxjRWIxA66GGMHjQ=
		</data>
		<key>Resources/PIL/_avif.pyi</key>
		<data>
		/mDvtAru7imYuwckXU+Vca0Igl8=
		</data>
		<key>Resources/PIL/_binary.py</key>
		<data>
		Xh10GE1pTHiTvMggE1skUDXC/5E=
		</data>
		<key>Resources/PIL/_deprecate.py</key>
		<data>
		mjywkr0jyH6uZz/hpHp9JrDn/oQ=
		</data>
		<key>Resources/PIL/_imaging.pyi</key>
		<data>
		ytlEs9K8ZTJhjhzZ4YYdqhY5euo=
		</data>
		<key>Resources/PIL/_imagingcms.pyi</key>
		<data>
		lpLLyFGYfeDqJoKwvb+AAZ2THz0=
		</data>
		<key>Resources/PIL/_imagingft.pyi</key>
		<data>
		BxJj11RWU/Tc4rsrOaNmIDN7B3I=
		</data>
		<key>Resources/PIL/_imagingmath.pyi</key>
		<data>
		/mDvtAru7imYuwckXU+Vca0Igl8=
		</data>
		<key>Resources/PIL/_imagingmorph.pyi</key>
		<data>
		/mDvtAru7imYuwckXU+Vca0Igl8=
		</data>
		<key>Resources/PIL/_imagingtk.pyi</key>
		<data>
		/mDvtAru7imYuwckXU+Vca0Igl8=
		</data>
		<key>Resources/PIL/_tkinter_finder.py</key>
		<data>
		yyxdZNdrQ54urIiGJfYXFc0CCTw=
		</data>
		<key>Resources/PIL/_typing.py</key>
		<data>
		hGsBgxXV+bMI17wDOcy+CDQM8WE=
		</data>
		<key>Resources/PIL/_util.py</key>
		<data>
		dOiC/hXrHDbnsoOw+J+o0WGg2qw=
		</data>
		<key>Resources/PIL/_version.py</key>
		<data>
		VHny+94GhqxhJxxlQoF4s2469zc=
		</data>
		<key>Resources/PIL/_webp.pyi</key>
		<data>
		/mDvtAru7imYuwckXU+Vca0Igl8=
		</data>
		<key>Resources/PIL/features.py</key>
		<data>
		zAV/O3uGO3ro6BJBRClzSn8FWW8=
		</data>
		<key>Resources/PIL/py.typed</key>
		<data>
		2jmj7l5rSw0yVb/vlWAYkK/YBwk=
		</data>
		<key>Resources/PIL/report.py</key>
		<data>
		W3HeUe8v91YeUhwXPRXf7DZvUG4=
		</data>
		<key>Resources/_tcl_data/auto.tcl</key>
		<data>
		q3LD5+JJwylBqn63fZx2SxIFWog=
		</data>
		<key>Resources/_tcl_data/clock.tcl</key>
		<data>
		GI02QfnLLcRREBDxjNpDXSgroas=
		</data>
		<key>Resources/_tcl_data/encoding/ascii.enc</key>
		<data>
		3YMzPcHIOL65EC8GOXHMwgzE/YA=
		</data>
		<key>Resources/_tcl_data/encoding/big5.enc</key>
		<data>
		njXr89U4DjS5L+J0QST5MkuQHdM=
		</data>
		<key>Resources/_tcl_data/encoding/cns11643.enc</key>
		<data>
		QIEYQ6+V587xeVcQF9f6adFErXg=
		</data>
		<key>Resources/_tcl_data/encoding/cp1250.enc</key>
		<data>
		+iJxAw25AF1x+q1gtEdnlV1UMt0=
		</data>
		<key>Resources/_tcl_data/encoding/cp1251.enc</key>
		<data>
		YEOW2B/S2Q9XNP5sPyg/jxmqu2Q=
		</data>
		<key>Resources/_tcl_data/encoding/cp1252.enc</key>
		<data>
		LiEwDgvIqEfQQjZxsI08ZXYe4XI=
		</data>
		<key>Resources/_tcl_data/encoding/cp1253.enc</key>
		<data>
		j/mlJqVF0pOCmmeaLs3TOqb5qQ4=
		</data>
		<key>Resources/_tcl_data/encoding/cp1254.enc</key>
		<data>
		SEcFppWWydgT6jYWJcOkXGuzEig=
		</data>
		<key>Resources/_tcl_data/encoding/cp1255.enc</key>
		<data>
		Zg2+RYOSPL3/9iYbH630NJZYV5w=
		</data>
		<key>Resources/_tcl_data/encoding/cp1256.enc</key>
		<data>
		OP7jn0ThTDohmXj4tuTaVIFSz9Y=
		</data>
		<key>Resources/_tcl_data/encoding/cp1257.enc</key>
		<data>
		zBA8U7O6F2RxRYfq69ks0bx1GU0=
		</data>
		<key>Resources/_tcl_data/encoding/cp1258.enc</key>
		<data>
		YpTkLtItdWef8UZP9B1D2zsYJMI=
		</data>
		<key>Resources/_tcl_data/encoding/cp437.enc</key>
		<data>
		P3JSRcZgUNOdkjS6rOnQR6OEKUQ=
		</data>
		<key>Resources/_tcl_data/encoding/cp737.enc</key>
		<data>
		yV6k7T++8BPYEMC/sZOxX6it57g=
		</data>
		<key>Resources/_tcl_data/encoding/cp775.enc</key>
		<data>
		9DAaE0ChYOHygrX5i/n6y/qTsRk=
		</data>
		<key>Resources/_tcl_data/encoding/cp850.enc</key>
		<data>
		Xq03eI0STU7knsS4qhz2qqnChJw=
		</data>
		<key>Resources/_tcl_data/encoding/cp852.enc</key>
		<data>
		kEs1fDBgPfvPihCgVNk5lgixMd8=
		</data>
		<key>Resources/_tcl_data/encoding/cp855.enc</key>
		<data>
		+L1L9tlfZyy2G47KtYCnZb69rqU=
		</data>
		<key>Resources/_tcl_data/encoding/cp857.enc</key>
		<data>
		iISZ2d/fdcYMJ3A4akUA81dTznA=
		</data>
		<key>Resources/_tcl_data/encoding/cp860.enc</key>
		<data>
		xrHpMg7vRvyaI0N8JV5AheopgNs=
		</data>
		<key>Resources/_tcl_data/encoding/cp861.enc</key>
		<data>
		U1KXcupjIreUnbc+667ZHlpbo9o=
		</data>
		<key>Resources/_tcl_data/encoding/cp862.enc</key>
		<data>
		7yc2cdRoFfIplupjLSLMJ+uMpEs=
		</data>
		<key>Resources/_tcl_data/encoding/cp863.enc</key>
		<data>
		f27Ym9DUFcZNC4oDfwikf+rdFMQ=
		</data>
		<key>Resources/_tcl_data/encoding/cp864.enc</key>
		<data>
		4MpAC64PZu6+Tf4UfFoY3TsAt4w=
		</data>
		<key>Resources/_tcl_data/encoding/cp865.enc</key>
		<data>
		DrQP7rijglMLaXSOCL9RMSQjJAM=
		</data>
		<key>Resources/_tcl_data/encoding/cp866.enc</key>
		<data>
		QxJwk50+R5v5uaZj2eZ/zrp5QW8=
		</data>
		<key>Resources/_tcl_data/encoding/cp869.enc</key>
		<data>
		6BSfMzsYCdzN5Rz4tjMhA93n/DA=
		</data>
		<key>Resources/_tcl_data/encoding/cp874.enc</key>
		<data>
		2gV+H5P3VSGlHMcl1HEw9B5QnnA=
		</data>
		<key>Resources/_tcl_data/encoding/cp932.enc</key>
		<data>
		0LPesO5lOc5fKKUUZL+7OqA/KOU=
		</data>
		<key>Resources/_tcl_data/encoding/cp936.enc</key>
		<data>
		Ox0HsCrn47QHhIceF/NjMoNCaOY=
		</data>
		<key>Resources/_tcl_data/encoding/cp949.enc</key>
		<data>
		kEqLeEbTRSFjTIwJAT27HTGvR8o=
		</data>
		<key>Resources/_tcl_data/encoding/cp950.enc</key>
		<data>
		ZgX8yyNaCPkDK7RSMbGmMxdkZks=
		</data>
		<key>Resources/_tcl_data/encoding/dingbats.enc</key>
		<data>
		aiHVe0SghWq83mGxwWy5P05MPXQ=
		</data>
		<key>Resources/_tcl_data/encoding/ebcdic.enc</key>
		<data>
		RlUJxybEloCwI3JQGvelLwmrfVU=
		</data>
		<key>Resources/_tcl_data/encoding/euc-cn.enc</key>
		<data>
		vMyJkBW2iNXEJrx5HC/N46A6PrU=
		</data>
		<key>Resources/_tcl_data/encoding/euc-jp.enc</key>
		<data>
		+PyjmFAJos3Tl8s7rjCK8FsNfKw=
		</data>
		<key>Resources/_tcl_data/encoding/euc-kr.enc</key>
		<data>
		ic2k/mUVycA1UeThly/UeK86QZw=
		</data>
		<key>Resources/_tcl_data/encoding/gb12345.enc</key>
		<data>
		XI5pGuPBMwiCD0z2kgbXZc/VCUs=
		</data>
		<key>Resources/_tcl_data/encoding/gb1988.enc</key>
		<data>
		ScZjrCbB/k8P0UKMnvJwWK7mypU=
		</data>
		<key>Resources/_tcl_data/encoding/gb2312-raw.enc</key>
		<data>
		26CcZw8k1HuV0S1LuXBDkbgd2po=
		</data>
		<key>Resources/_tcl_data/encoding/gb2312.enc</key>
		<data>
		vMyJkBW2iNXEJrx5HC/N46A6PrU=
		</data>
		<key>Resources/_tcl_data/encoding/iso2022-jp.enc</key>
		<data>
		OeILQc+osmk3evoG+cTWbt2Uass=
		</data>
		<key>Resources/_tcl_data/encoding/iso2022-kr.enc</key>
		<data>
		lMXzklY2artozWfjAl8Xf1Ts050=
		</data>
		<key>Resources/_tcl_data/encoding/iso2022.enc</key>
		<data>
		nW8HdZilqG5utqTuwUgQv1JfvYk=
		</data>
		<key>Resources/_tcl_data/encoding/iso8859-1.enc</key>
		<data>
		pxtjgOo9I9wN4R07jOqGpMgGPUc=
		</data>
		<key>Resources/_tcl_data/encoding/iso8859-10.enc</key>
		<data>
		zseHxN54+du5e5xEBwzywSokaPc=
		</data>
		<key>Resources/_tcl_data/encoding/iso8859-11.enc</key>
		<data>
		AcVG7nwQsWSnTWY/pvwrx9MhIVU=
		</data>
		<key>Resources/_tcl_data/encoding/iso8859-13.enc</key>
		<data>
		1GKTSgdO4T8sgQRj/QYQhJU/d7w=
		</data>
		<key>Resources/_tcl_data/encoding/iso8859-14.enc</key>
		<data>
		YsJTqnqGjOMliYaPqzczZUJFepY=
		</data>
		<key>Resources/_tcl_data/encoding/iso8859-15.enc</key>
		<data>
		SfekKIn7ig14yABnveGAlNvpVu4=
		</data>
		<key>Resources/_tcl_data/encoding/iso8859-16.enc</key>
		<data>
		UP2mxwoTPLZM84qksvMTtU0v2VU=
		</data>
		<key>Resources/_tcl_data/encoding/iso8859-2.enc</key>
		<data>
		/2LrVxD94RB0qH2u6SKbz39m16A=
		</data>
		<key>Resources/_tcl_data/encoding/iso8859-3.enc</key>
		<data>
		sL6+3sU/+4lNn7DVfyWrKkWbbdU=
		</data>
		<key>Resources/_tcl_data/encoding/iso8859-4.enc</key>
		<data>
		zBwubDWwBcF+t7Gj10SYOoanVzY=
		</data>
		<key>Resources/_tcl_data/encoding/iso8859-5.enc</key>
		<data>
		+fZLtgFAaOLAc3GGxpS4EB3ZV14=
		</data>
		<key>Resources/_tcl_data/encoding/iso8859-6.enc</key>
		<data>
		uBBCY1TYV3GMyEHUJNoHDvufFE8=
		</data>
		<key>Resources/_tcl_data/encoding/iso8859-7.enc</key>
		<data>
		eswpSNXomXUMKsbIFM3hfUIRK8k=
		</data>
		<key>Resources/_tcl_data/encoding/iso8859-8.enc</key>
		<data>
		TqXsUzFUHt5lqc9gH1QY/Uts/Lw=
		</data>
		<key>Resources/_tcl_data/encoding/iso8859-9.enc</key>
		<data>
		9YXHClWJ3jlVjawBZ0P/heDF8DI=
		</data>
		<key>Resources/_tcl_data/encoding/jis0201.enc</key>
		<data>
		eutwjInBePtNVhHCReoafPZq3zo=
		</data>
		<key>Resources/_tcl_data/encoding/jis0208.enc</key>
		<data>
		qSKsrODBpKfdyS/l3XoRbTCjaGs=
		</data>
		<key>Resources/_tcl_data/encoding/jis0212.enc</key>
		<data>
		FQE0eHYEY6C841d7TWRuzbB2MrU=
		</data>
		<key>Resources/_tcl_data/encoding/koi8-r.enc</key>
		<data>
		NmwTfALgabGpP7tdZLkSDqbprR8=
		</data>
		<key>Resources/_tcl_data/encoding/koi8-ru.enc</key>
		<data>
		lccx/oU6osxBfUUWrvtLLwReU2M=
		</data>
		<key>Resources/_tcl_data/encoding/koi8-t.enc</key>
		<data>
		vC1lEb+iw4MbVfpGqYkdE9raLmU=
		</data>
		<key>Resources/_tcl_data/encoding/koi8-u.enc</key>
		<data>
		qpbuTHWhrTV2lMjbwUaEgnhbgVs=
		</data>
		<key>Resources/_tcl_data/encoding/ksc5601.enc</key>
		<data>
		wv+kJ0V7STHlqSMm8lHNPWcQWbA=
		</data>
		<key>Resources/_tcl_data/encoding/macCentEuro.enc</key>
		<data>
		FrUdAXABaIoyy3sV3m56SfKLdv0=
		</data>
		<key>Resources/_tcl_data/encoding/macCroatian.enc</key>
		<data>
		Y/ROgYKEOE3gerDYsM1vfr/gmrk=
		</data>
		<key>Resources/_tcl_data/encoding/macCyrillic.enc</key>
		<data>
		PeF7KlhmJyYC+46cVJMKTNHzsGw=
		</data>
		<key>Resources/_tcl_data/encoding/macDingbats.enc</key>
		<data>
		pA5tuX1tsok6Bysiddwi4qTWBzc=
		</data>
		<key>Resources/_tcl_data/encoding/macGreek.enc</key>
		<data>
		nCrVPWn1B3hToF8JMzMLXW+IpRw=
		</data>
		<key>Resources/_tcl_data/encoding/macIceland.enc</key>
		<data>
		xDQlfXap/fgczNjMFCQsjjlA/Yk=
		</data>
		<key>Resources/_tcl_data/encoding/macJapan.enc</key>
		<data>
		uhh8UvrpeS2lv/vqp4H9TgcW4PY=
		</data>
		<key>Resources/_tcl_data/encoding/macRoman.enc</key>
		<data>
		5Njqag5wu3kzBMoh6xM3p6LCajE=
		</data>
		<key>Resources/_tcl_data/encoding/macRomania.enc</key>
		<data>
		4le9Fu80/cKdW2yYWhtFgBk3NUw=
		</data>
		<key>Resources/_tcl_data/encoding/macThai.enc</key>
		<data>
		Yz0ZC14oHPwBePbBHdchxqJm9kM=
		</data>
		<key>Resources/_tcl_data/encoding/macTurkish.enc</key>
		<data>
		OC40gkrYt57wyY/VFnUGSf2Usgo=
		</data>
		<key>Resources/_tcl_data/encoding/macUkraine.enc</key>
		<data>
		pIeUbLLv11/XSFA9deSVcgtT5bw=
		</data>
		<key>Resources/_tcl_data/encoding/shiftjis.enc</key>
		<data>
		LVaWWyQSXZmdECDHw0e4E6lyZHw=
		</data>
		<key>Resources/_tcl_data/encoding/symbol.enc</key>
		<data>
		8MAUttZ/wNwdG7xfBS8Mixxj2L8=
		</data>
		<key>Resources/_tcl_data/encoding/tis-620.enc</key>
		<data>
		RvsXtOCFAQIomg0D9PlWmzqkmU0=
		</data>
		<key>Resources/_tcl_data/history.tcl</key>
		<data>
		ZBritOdlyGpWPibSy382Xk3iffI=
		</data>
		<key>Resources/_tcl_data/http1.0/http.tcl</key>
		<data>
		vfJdcuRoG+zM5TTejFSRWY+hwQY=
		</data>
		<key>Resources/_tcl_data/http1.0/pkgIndex.tcl</key>
		<data>
		YAGligcB3/Il4lEKSq7mSJpTdlc=
		</data>
		<key>Resources/_tcl_data/init.tcl</key>
		<data>
		08ihDRHGSRyW81t0FEMvBCVl7fQ=
		</data>
		<key>Resources/_tcl_data/opt0.4/optparse.tcl</key>
		<data>
		mAKWQQWnNjvKScwDJu2+mRtW1SY=
		</data>
		<key>Resources/_tcl_data/opt0.4/pkgIndex.tcl</key>
		<data>
		BijHt83cyQHdeo+3uC/IUHDAcs8=
		</data>
		<key>Resources/_tcl_data/package.tcl</key>
		<data>
		xvz8AIOLCxb357dR8n3RFRbKUP8=
		</data>
		<key>Resources/_tcl_data/parray.tcl</key>
		<data>
		oLG9Tmjc4XaNPF4NPHsx4oAh07o=
		</data>
		<key>Resources/_tcl_data/safe.tcl</key>
		<data>
		DbqULAyylaDKpaN5gZuUTeT4SRc=
		</data>
		<key>Resources/_tcl_data/tclAppInit.c</key>
		<data>
		km85h6X//lSHequmKjsANnlOM48=
		</data>
		<key>Resources/_tcl_data/tclDTrace.d</key>
		<data>
		MMrg/n4rY5zjhot+mX06uuK1DAI=
		</data>
		<key>Resources/_tcl_data/tclIndex</key>
		<data>
		TJBi9Pynggw4xKNop0EugaDiuBg=
		</data>
		<key>Resources/_tcl_data/tm.tcl</key>
		<data>
		s1YLIYyMARZeSFvx/qCPB3iCchs=
		</data>
		<key>Resources/_tcl_data/word.tcl</key>
		<data>
		EHWAJskYyJheR9QvBglh+o7+vdk=
		</data>
		<key>Resources/_tk_data/bgerror.tcl</key>
		<data>
		ThycTe1bKIcdTJwWNWggUgaxihw=
		</data>
		<key>Resources/_tk_data/button.tcl</key>
		<data>
		PguQVCjCk/IQdBRf5DKB8i5pnrQ=
		</data>
		<key>Resources/_tk_data/choosedir.tcl</key>
		<data>
		b4+6WX8IUIPK3Y5xk9F2/qCinOc=
		</data>
		<key>Resources/_tk_data/clrpick.tcl</key>
		<data>
		TAQKzEzSEXP49phjV0NARrvQzDc=
		</data>
		<key>Resources/_tk_data/comdlg.tcl</key>
		<data>
		UeK1uukZTnsbvTTK/Ko9IrXhxrc=
		</data>
		<key>Resources/_tk_data/console.tcl</key>
		<data>
		FhAPdcjHT8PgEH9Jim/hl7gUi3k=
		</data>
		<key>Resources/_tk_data/dialog.tcl</key>
		<data>
		wYBuvfc08XAqPlQl+5FaAImE8Hw=
		</data>
		<key>Resources/_tk_data/entry.tcl</key>
		<data>
		UboQkLAvC5oVA8URpH0RFzlOE8E=
		</data>
		<key>Resources/_tk_data/focus.tcl</key>
		<data>
		aDWlFehanlXVonBz2uHxpddCRRM=
		</data>
		<key>Resources/_tk_data/fontchooser.tcl</key>
		<data>
		e6wFZyls0Z4ft+nRs67in1XccN4=
		</data>
		<key>Resources/_tk_data/iconlist.tcl</key>
		<data>
		o675ntOsggS/0Q32SWt/Lp3Lsuk=
		</data>
		<key>Resources/_tk_data/icons.tcl</key>
		<data>
		Jq4ArEnwnaDs/x2y8CGYOCO3fxI=
		</data>
		<key>Resources/_tk_data/images/README</key>
		<data>
		8boyJEjSBmI/j+c0GS84PY9/oZg=
		</data>
		<key>Resources/_tk_data/images/logo.eps</key>
		<data>
		K0mbfE68hVTswHuECGMsr0B/ttU=
		</data>
		<key>Resources/_tk_data/images/logo100.gif</key>
		<data>
		vLc9ivJihGOhuVVYGZnHfwn4Bbg=
		</data>
		<key>Resources/_tk_data/images/logo64.gif</key>
		<data>
		6lIhmjehQP2YrqZupUaF3YFY2bE=
		</data>
		<key>Resources/_tk_data/images/logoLarge.gif</key>
		<data>
		3dEOeYryCe/OAi6XRI5e4RzrViE=
		</data>
		<key>Resources/_tk_data/images/logoMed.gif</key>
		<data>
		E0iOTyhnbx4M44P4DRNRDwcZi5k=
		</data>
		<key>Resources/_tk_data/images/pwrdLogo.eps</key>
		<data>
		ilj8GbIL/ciRNRXZsyzL+Kz5I0Q=
		</data>
		<key>Resources/_tk_data/images/pwrdLogo100.gif</key>
		<data>
		vZceca6AXCwuUd1UTQBukjY7bAw=
		</data>
		<key>Resources/_tk_data/images/pwrdLogo150.gif</key>
		<data>
		GhWCZQ4hiwvm/97/1k0n9Lmphw8=
		</data>
		<key>Resources/_tk_data/images/pwrdLogo175.gif</key>
		<data>
		jbf7RTt5uPK05nrDCkultb3evTs=
		</data>
		<key>Resources/_tk_data/images/pwrdLogo200.gif</key>
		<data>
		+pig/YkQ3y77FO2uwDi045H+qzw=
		</data>
		<key>Resources/_tk_data/images/pwrdLogo75.gif</key>
		<data>
		5bHe1JCVMyI2Q5U47NndCx/Uk0s=
		</data>
		<key>Resources/_tk_data/images/tai-ku.gif</key>
		<data>
		JnqVIMQ5AiHc5QF354mk69WQ9IQ=
		</data>
		<key>Resources/_tk_data/listbox.tcl</key>
		<data>
		qiZADGyG6c17jazOTKuAt64hqXg=
		</data>
		<key>Resources/_tk_data/megawidget.tcl</key>
		<data>
		n4k0mIglWV9zEc/w0OGn+45GNKQ=
		</data>
		<key>Resources/_tk_data/menu.tcl</key>
		<data>
		Ltd65Ojk6Jd8RhBBQDplHK9/c3A=
		</data>
		<key>Resources/_tk_data/mkpsenc.tcl</key>
		<data>
		kl2XO3AlI4TR3ps4jGwgOOZG/d8=
		</data>
		<key>Resources/_tk_data/msgbox.tcl</key>
		<data>
		RsGIL5eKTXpu0NLyIO3L2J27+z8=
		</data>
		<key>Resources/_tk_data/msgs/cs.msg</key>
		<data>
		lebHHkUlqN2R5Ii5UmZa6cX73e0=
		</data>
		<key>Resources/_tk_data/msgs/da.msg</key>
		<data>
		UprgsMudHbx/iETzRhSeFR3go2s=
		</data>
		<key>Resources/_tk_data/msgs/de.msg</key>
		<data>
		y0VfkQII4uVbJ6lqvYRf7tqIcRo=
		</data>
		<key>Resources/_tk_data/msgs/el.msg</key>
		<data>
		JVMd9iYuOxFwBVc1xah0uRJP6oM=
		</data>
		<key>Resources/_tk_data/msgs/en.msg</key>
		<data>
		V1QprquvZkBCWsG8OXszgsHtESI=
		</data>
		<key>Resources/_tk_data/msgs/en_gb.msg</key>
		<data>
		bC1rYiQpq4wX4HwuD1RkaYI6vlc=
		</data>
		<key>Resources/_tk_data/msgs/eo.msg</key>
		<data>
		pY/pWpYS3lzav/XdzuABmitlFgE=
		</data>
		<key>Resources/_tk_data/msgs/es.msg</key>
		<data>
		jDUW95+3LzKEi0AJHaZ8geQP3v4=
		</data>
		<key>Resources/_tk_data/msgs/fi.msg</key>
		<data>
		fepoJVjo3Oc47h97QpfMX5e180I=
		</data>
		<key>Resources/_tk_data/msgs/fr.msg</key>
		<data>
		ytOAWQDoYLlJHj7lwsD1KtymcGU=
		</data>
		<key>Resources/_tk_data/msgs/hu.msg</key>
		<data>
		GhSNIwyfjXSNlqec1OJhryZNZSQ=
		</data>
		<key>Resources/_tk_data/msgs/it.msg</key>
		<data>
		X7FjvBCG0zZiKCBAePIZ/ku2fLM=
		</data>
		<key>Resources/_tk_data/msgs/nl.msg</key>
		<data>
		j2r/aLQrdH0whw1tp+BYKUkhQGo=
		</data>
		<key>Resources/_tk_data/msgs/pl.msg</key>
		<data>
		PkXAECsoeQjXcKMdGQZnjnhQiMI=
		</data>
		<key>Resources/_tk_data/msgs/pt.msg</key>
		<data>
		lI7pX0VJ2ox9QSkR0XtLYsuiKt0=
		</data>
		<key>Resources/_tk_data/msgs/ru.msg</key>
		<data>
		FpDFn6nzZwDY86ptWYUAjuEls/Q=
		</data>
		<key>Resources/_tk_data/msgs/sv.msg</key>
		<data>
		KNnbnL7nkcCb0nLZwqbD2oDrieo=
		</data>
		<key>Resources/_tk_data/msgs/zh_cn.msg</key>
		<data>
		CRYOLe02340zZ4hksZ+HFZjuKSA=
		</data>
		<key>Resources/_tk_data/obsolete.tcl</key>
		<data>
		MOJFld1oPkcP6fEoFNJ9bSZrUR4=
		</data>
		<key>Resources/_tk_data/optMenu.tcl</key>
		<data>
		l0XIPuyFZWAvjXRhBCSEgAn/pnA=
		</data>
		<key>Resources/_tk_data/palette.tcl</key>
		<data>
		oyLM+zP/c+SkcwtbId5CkPnZRiI=
		</data>
		<key>Resources/_tk_data/panedwindow.tcl</key>
		<data>
		3+PcZjwZ6aUFJqUTBD0jk4adj5A=
		</data>
		<key>Resources/_tk_data/pkgIndex.tcl</key>
		<data>
		XOp1V7FWeHT9who12RwpezekOys=
		</data>
		<key>Resources/_tk_data/safetk.tcl</key>
		<data>
		0ZOw5gErQuu06VsONbGpzawlIhw=
		</data>
		<key>Resources/_tk_data/scale.tcl</key>
		<data>
		PlppetqYRK09qYBe2RIx0nCM88E=
		</data>
		<key>Resources/_tk_data/scrlbar.tcl</key>
		<data>
		I+b3CVBm7TtlmYMkAh1mXYEOapM=
		</data>
		<key>Resources/_tk_data/spinbox.tcl</key>
		<data>
		mxZmqeyYka/MzK+9XM/L/pBOhdQ=
		</data>
		<key>Resources/_tk_data/tclIndex</key>
		<data>
		/CEDJvFTjko57lytP8tP81KHJCk=
		</data>
		<key>Resources/_tk_data/tearoff.tcl</key>
		<data>
		qKEoPyejUVzAzyijvYfWV79Vnh8=
		</data>
		<key>Resources/_tk_data/text.tcl</key>
		<data>
		TLr1vX72+ATQqphoF0bhQ/ytO2I=
		</data>
		<key>Resources/_tk_data/tk.tcl</key>
		<data>
		umpL3Zq1rruoo8r+3eHBvRKb1Ko=
		</data>
		<key>Resources/_tk_data/tkAppInit.c</key>
		<data>
		8JMX4S2O4t0KVuZHQ2za78mjZAQ=
		</data>
		<key>Resources/_tk_data/tkfbox.tcl</key>
		<data>
		LQrPWwmF+yYZY9yq4DNHdjQ44xE=
		</data>
		<key>Resources/_tk_data/ttk/altTheme.tcl</key>
		<data>
		1GsRcUh7gTBmjeeUo8x+dqDlLj4=
		</data>
		<key>Resources/_tk_data/ttk/aquaTheme.tcl</key>
		<data>
		sotleIKW5LSsMJKoV94sVjC0GAo=
		</data>
		<key>Resources/_tk_data/ttk/button.tcl</key>
		<data>
		AXomewLff3oqIW8UuGgM/wQhQBE=
		</data>
		<key>Resources/_tk_data/ttk/clamTheme.tcl</key>
		<data>
		Qg0b3exeM+jh8zfg7vI2Yct6g1o=
		</data>
		<key>Resources/_tk_data/ttk/classicTheme.tcl</key>
		<data>
		1emFlDqkPq+ljpDSvsdIW2doe9Y=
		</data>
		<key>Resources/_tk_data/ttk/combobox.tcl</key>
		<data>
		AdLHr5j79DIwP828h6T2cZ0Pc7A=
		</data>
		<key>Resources/_tk_data/ttk/cursors.tcl</key>
		<data>
		DddlHcbhwfX1FmoNlY7+BUW+gJ0=
		</data>
		<key>Resources/_tk_data/ttk/defaults.tcl</key>
		<data>
		UDCWOoBCTzoHaSFAsdAoGqK/y50=
		</data>
		<key>Resources/_tk_data/ttk/entry.tcl</key>
		<data>
		mV33YkSBrPKdFCtlrr5NUjIuYl0=
		</data>
		<key>Resources/_tk_data/ttk/fonts.tcl</key>
		<data>
		Snv5G+hfEN/He3uOA7N+NO5R7q8=
		</data>
		<key>Resources/_tk_data/ttk/menubutton.tcl</key>
		<data>
		lcLabSCzCUXS2BZhbPyTH0iOn/c=
		</data>
		<key>Resources/_tk_data/ttk/notebook.tcl</key>
		<data>
		rwHBo5Y9koMB1rwslNDYfd3uA0Q=
		</data>
		<key>Resources/_tk_data/ttk/panedwindow.tcl</key>
		<data>
		rD6tifSHR/5zAcKGVBDQ1cj/YA0=
		</data>
		<key>Resources/_tk_data/ttk/progress.tcl</key>
		<data>
		0HCgHMWnhySbxtrRhLJJxN03OWo=
		</data>
		<key>Resources/_tk_data/ttk/scale.tcl</key>
		<data>
		g2QnCoeAp1uLLC1RceoH+ianNls=
		</data>
		<key>Resources/_tk_data/ttk/scrollbar.tcl</key>
		<data>
		N41GZpiT3gQWS4o/QB/O628B84g=
		</data>
		<key>Resources/_tk_data/ttk/sizegrip.tcl</key>
		<data>
		pFjqXy3bu8ySGz0+YAzkIGUmTmE=
		</data>
		<key>Resources/_tk_data/ttk/spinbox.tcl</key>
		<data>
		9qZnarr+F4GNITEpohU2bzkLgQs=
		</data>
		<key>Resources/_tk_data/ttk/treeview.tcl</key>
		<data>
		I9LaoEaT61JA5Ax0PL/wYUt8Bxk=
		</data>
		<key>Resources/_tk_data/ttk/ttk.tcl</key>
		<data>
		PdNEvbMBQO29Z6AwhgDzXN2Tht0=
		</data>
		<key>Resources/_tk_data/ttk/utils.tcl</key>
		<data>
		UsD05zN5bJ2bs2qKjFqaXoxHzm8=
		</data>
		<key>Resources/_tk_data/ttk/vistaTheme.tcl</key>
		<data>
		xD5OERTTumziooC+SGXiSsnNyks=
		</data>
		<key>Resources/_tk_data/ttk/winTheme.tcl</key>
		<data>
		TZUB1UV/62RgzeyMwOjfH05WsTU=
		</data>
		<key>Resources/_tk_data/ttk/xpTheme.tcl</key>
		<data>
		IMDfjiaT+VHMgaH5AnROAEzboCM=
		</data>
		<key>Resources/_tk_data/unsupported.tcl</key>
		<data>
		gIL95QxCjSURsF9Sn8zwJlHVrJM=
		</data>
		<key>Resources/_tk_data/xmfbox.tcl</key>
		<data>
		GBqdjL2eeFxpGUmsJTamHaS0USo=
		</data>
		<key>Resources/base_library.zip</key>
		<data>
		MtLN2NQeOi+bFWIOJ/yW5M7LVbU=
		</data>
		<key>Resources/customtkinter-5.2.2.dist-info/INSTALLER</key>
		<data>
		16AxQdXWseiLa1nvCLZoHfISxZk=
		</data>
		<key>Resources/customtkinter-5.2.2.dist-info/LICENSE</key>
		<data>
		lCF2tjngH+2La9WP3MHn3GYERy8=
		</data>
		<key>Resources/customtkinter-5.2.2.dist-info/METADATA</key>
		<data>
		ojYn6BBIF5zJtRpIqojODrvKl1M=
		</data>
		<key>Resources/customtkinter-5.2.2.dist-info/RECORD</key>
		<data>
		3M3ujEm3gbVlXyNSyPpNKFuhZ7Y=
		</data>
		<key>Resources/customtkinter-5.2.2.dist-info/REQUESTED</key>
		<data>
		2jmj7l5rSw0yVb/vlWAYkK/YBwk=
		</data>
		<key>Resources/customtkinter-5.2.2.dist-info/WHEEL</key>
		<data>
		UC+V2jCJVJ4ZxFFzeqJi5Fxbw7w=
		</data>
		<key>Resources/customtkinter-5.2.2.dist-info/top_level.txt</key>
		<data>
		/2MNh/0+h5zQ4/Os09fJ5LIi7s4=
		</data>
		<key>Resources/customtkinter/__init__.py</key>
		<data>
		Wln+Qw0tEjfEuvROIKJm7gguSEs=
		</data>
		<key>Resources/customtkinter/assets/fonts/CustomTkinter_shapes_font.otf</key>
		<data>
		INBhs7dCz6MeX7yGLTT1V1NO/b8=
		</data>
		<key>Resources/customtkinter/assets/fonts/Roboto/Roboto-Medium.ttf</key>
		<data>
		/dyLHGiO87rtDVpGq/XwHw7a8Cs=
		</data>
		<key>Resources/customtkinter/assets/fonts/Roboto/Roboto-Regular.ttf</key>
		<data>
		hNECSIc4sOu8elCHlz7/vVTJW9U=
		</data>
		<key>Resources/customtkinter/assets/icons/CustomTkinter_icon_Windows.ico</key>
		<data>
		s2ExZOWH0JwFLDTM3E1E2sT/ROI=
		</data>
		<key>Resources/customtkinter/assets/themes/blue.json</key>
		<data>
		Bv/IEe5RYJgJ2IiUAi4iKzOa7+4=
		</data>
		<key>Resources/customtkinter/assets/themes/dark-blue.json</key>
		<data>
		+evgfnnhRvedyIp/+JQsDkMEnw0=
		</data>
		<key>Resources/customtkinter/assets/themes/green.json</key>
		<data>
		TwB32rbJhqZKuTkmMAJMsJdyseg=
		</data>
		<key>Resources/customtkinter/windows/__init__.py</key>
		<data>
		AXewfnqN2cPZWrlNUOH0rZhgE7A=
		</data>
		<key>Resources/customtkinter/windows/ctk_input_dialog.py</key>
		<data>
		qSCFzm7g9oLaNqpIJMcLjHf7Bc4=
		</data>
		<key>Resources/customtkinter/windows/ctk_tk.py</key>
		<data>
		RZn0egqCBDOEiXhq9HMpF/KRn0c=
		</data>
		<key>Resources/customtkinter/windows/ctk_toplevel.py</key>
		<data>
		zdwCZ0vykVwxFvchH7u7SJXsyLU=
		</data>
		<key>Resources/customtkinter/windows/widgets/__init__.py</key>
		<data>
		NLtGXvMXOGjzl+HX9s84CV7gFwA=
		</data>
		<key>Resources/customtkinter/windows/widgets/appearance_mode/__init__.py</key>
		<data>
		WualDnHMf0Wdr1nGiXFo3sf1Q0Y=
		</data>
		<key>Resources/customtkinter/windows/widgets/appearance_mode/appearance_mode_base_class.py</key>
		<data>
		67qSTVWF6ipthpMxiQ/JOUIr2rw=
		</data>
		<key>Resources/customtkinter/windows/widgets/appearance_mode/appearance_mode_tracker.py</key>
		<data>
		r5uR2pG0IVovgE5AU9i3b8dfAGo=
		</data>
		<key>Resources/customtkinter/windows/widgets/core_rendering/__init__.py</key>
		<data>
		+o2+YODiaiuggPPrb5Cl0vGp/Oo=
		</data>
		<key>Resources/customtkinter/windows/widgets/core_rendering/ctk_canvas.py</key>
		<data>
		xn7Sx4zGdnTxI4GHtdXVETtVm78=
		</data>
		<key>Resources/customtkinter/windows/widgets/core_rendering/draw_engine.py</key>
		<data>
		ojZuYVXjCkIgcLsohWog5oM1CN0=
		</data>
		<key>Resources/customtkinter/windows/widgets/core_widget_classes/__init__.py</key>
		<data>
		at5fLm39WwpKIVSGQ4CatYcbGpk=
		</data>
		<key>Resources/customtkinter/windows/widgets/core_widget_classes/ctk_base_class.py</key>
		<data>
		3r+t6ku1t4rv2CYmhJYzek9192M=
		</data>
		<key>Resources/customtkinter/windows/widgets/core_widget_classes/dropdown_menu.py</key>
		<data>
		YLCnwzZUoLDGhiIVmY1aNF9J+Mk=
		</data>
		<key>Resources/customtkinter/windows/widgets/ctk_button.py</key>
		<data>
		hL7fPuTeOE8JX/nCmF1agDYdSQU=
		</data>
		<key>Resources/customtkinter/windows/widgets/ctk_checkbox.py</key>
		<data>
		SKY9XY8AYnVfnY6Xt742sBbkCzY=
		</data>
		<key>Resources/customtkinter/windows/widgets/ctk_combobox.py</key>
		<data>
		0R1YvJ9PfAXy9bqqyHxeyFHdbjA=
		</data>
		<key>Resources/customtkinter/windows/widgets/ctk_entry.py</key>
		<data>
		1elVPws9ZuUIXDghjd/lE1xP1fo=
		</data>
		<key>Resources/customtkinter/windows/widgets/ctk_frame.py</key>
		<data>
		68Qx4SXoqMrhE6u5DHT2mEjfeF0=
		</data>
		<key>Resources/customtkinter/windows/widgets/ctk_label.py</key>
		<data>
		WkiWnC7OVjXd2H9D6f+kbZlCzbA=
		</data>
		<key>Resources/customtkinter/windows/widgets/ctk_optionmenu.py</key>
		<data>
		kKikSLPv5f9kH3a1w9yIkMOWKp8=
		</data>
		<key>Resources/customtkinter/windows/widgets/ctk_progressbar.py</key>
		<data>
		GtCBlk0WUEmYJJqqRDl867NMz+o=
		</data>
		<key>Resources/customtkinter/windows/widgets/ctk_radiobutton.py</key>
		<data>
		F3vvOznnrSdIQi5lxNwxZCPVhH0=
		</data>
		<key>Resources/customtkinter/windows/widgets/ctk_scrollable_frame.py</key>
		<data>
		lX0RUgyc7OU3jaB4YTq2ls/7FxM=
		</data>
		<key>Resources/customtkinter/windows/widgets/ctk_scrollbar.py</key>
		<data>
		f8NisTav3ahbJ6IaLZx2edgJNxQ=
		</data>
		<key>Resources/customtkinter/windows/widgets/ctk_segmented_button.py</key>
		<data>
		DZBt5jqwZ/sIa6gMqdUBOsYgvtE=
		</data>
		<key>Resources/customtkinter/windows/widgets/ctk_slider.py</key>
		<data>
		SWtK8a05rMVUshb8gcf1s9/GXYM=
		</data>
		<key>Resources/customtkinter/windows/widgets/ctk_switch.py</key>
		<data>
		oFmeJTKdwVw9lURm7K9zsen3pxA=
		</data>
		<key>Resources/customtkinter/windows/widgets/ctk_tabview.py</key>
		<data>
		YAYwjG6LJQw3wOxSpH+XMHVOcG8=
		</data>
		<key>Resources/customtkinter/windows/widgets/ctk_textbox.py</key>
		<data>
		vVDQCAh9Pnjq6qOmHVu4qxCm/co=
		</data>
		<key>Resources/customtkinter/windows/widgets/font/__init__.py</key>
		<data>
		M7kEVJFHYh4wCdun8tX/SWPZOa8=
		</data>
		<key>Resources/customtkinter/windows/widgets/font/ctk_font.py</key>
		<data>
		f83WvnTzAklSbVSvPwsGykC3YI8=
		</data>
		<key>Resources/customtkinter/windows/widgets/font/font_manager.py</key>
		<data>
		HiGTReaV/YEWFcX9fzdDi/xRkcs=
		</data>
		<key>Resources/customtkinter/windows/widgets/image/__init__.py</key>
		<data>
		m780kLv9afgV0QQwheBjmtOFykc=
		</data>
		<key>Resources/customtkinter/windows/widgets/image/ctk_image.py</key>
		<data>
		yiHJo2Rgm7NKk95zLYLlobYxYvs=
		</data>
		<key>Resources/customtkinter/windows/widgets/scaling/__init__.py</key>
		<data>
		npm5iUcMgYK2XzKFeCOrekteQQw=
		</data>
		<key>Resources/customtkinter/windows/widgets/scaling/scaling_base_class.py</key>
		<data>
		gLpwMzYjBP3CLztbHWJPDpwPxiY=
		</data>
		<key>Resources/customtkinter/windows/widgets/scaling/scaling_tracker.py</key>
		<data>
		sQlAqv7+IErafejTT0cUcCiLARw=
		</data>
		<key>Resources/customtkinter/windows/widgets/theme/__init__.py</key>
		<data>
		CiCr0sQnTlxvw8pAZlgPdJQ+uac=
		</data>
		<key>Resources/customtkinter/windows/widgets/theme/theme_manager.py</key>
		<data>
		UVpdgFN/gIj6uifRhfT4a/h5bmo=
		</data>
		<key>Resources/customtkinter/windows/widgets/utility/__init__.py</key>
		<data>
		5Yh9ARtnY0nRW+ttuAuli1j0jzc=
		</data>
		<key>Resources/customtkinter/windows/widgets/utility/utility_functions.py</key>
		<data>
		Ud9BJrZWgGxK8WjMwEizs6e4fq0=
		</data>
		<key>Resources/img/123.webp</key>
		<data>
		tU9ln24m/WldMzrodXCCV+zrzeE=
		</data>
		<key>Resources/img/bluetooth.icns</key>
		<data>
		BWQueLjvWPlbIq/Kh/iUbQpap6s=
		</data>
		<key>Resources/img/checkADE.icns</key>
		<data>
		KBz6o1IJg9OplEnKhcHyPj/T4Vw=
		</data>
		<key>Resources/img/device.icns</key>
		<data>
		sV33xFn17BTEvfFLUBfa2Yv1sxY=
		</data>
		<key>Resources/img/erase.png</key>
		<data>
		9BPaprJeiwNOEmv1HSsD2/Yi7mg=
		</data>
		<key>Resources/img/exit.png</key>
		<data>
		VWk+nE0rFweKNEjgXsZAgVd9rHI=
		</data>
		<key>Resources/img/findmy.icns</key>
		<data>
		EzmzWjuyERvTr2fQDimaOfU3Iqw=
		</data>
		<key>Resources/img/icons/atom.icns</key>
		<data>
		9xBtgDcBB88FI/NoaZD2Iqn7QDo=
		</data>
		<key>Resources/img/icons/atom/atom_256x256x32,atom_512x512x32,atom_1024x1024x32.zip</key>
		<data>
		3eMmugtk4h4YQmbeb4xdgmBj3T8=
		</data>
		<key>Resources/img/icons/atom/atom_256x256x32,atom_512x512x32,atom_1024x1024x32/atom_1024x1024x32.png</key>
		<data>
		E3VcMpe8BOXykBrRIS/Dop8PM4Y=
		</data>
		<key>Resources/img/icons/atom/atom_256x256x32,atom_512x512x32,atom_1024x1024x32/atom_256x256x32.png</key>
		<data>
		X/RU8sRaxRuwEJbx5FmjyxXeGQA=
		</data>
		<key>Resources/img/icons/atom/atom_256x256x32,atom_512x512x32,atom_1024x1024x32/atom_512x512x32.png</key>
		<data>
		ErhBVdJMreWSZyx2OmeTwe2zGA4=
		</data>
		<key>Resources/img/icons/maccheck_v2.png</key>
		<data>
		jbVkqR9Dcxhshr0szCdL+aQnCh8=
		</data>
		<key>Resources/img/icons/maccheck_v5.png</key>
		<data>
		lGHZW9mQd+sht3gaNBQd9t9VOx0=
		</data>
		<key>Resources/img/icons/vsmactool copy.png</key>
		<data>
		QLzYsQbFgsE5gZYymSDn3TzD5ak=
		</data>
		<key>Resources/img/icons/vsmactool-removebg-preview.png</key>
		<data>
		aIkm0K3dZus8pfnebWHovkQpJ6M=
		</data>
		<key>Resources/img/icons/vsmactool.icns</key>
		<data>
		KI4Cs1CKfHdxNBXwR1Hws0sco4g=
		</data>
		<key>Resources/img/icons/vsmactool.png</key>
		<data>
		tXmaRphrmfeCSmadjrFLDQOKyek=
		</data>
		<key>Resources/img/icons/vsmactool_1024.icns</key>
		<data>
		553Wlk6qQrLRLtksMnIb9OUweTU=
		</data>
		<key>Resources/img/icons/vsmactoolv4.icns</key>
		<data>
		IdzuszYSAphwJFmjJXrvbqAK1+Q=
		</data>
		<key>Resources/img/icons/xxx.icns</key>
		<data>
		/bZf6q4HmLDhO0YEfkZNtGl9Frw=
		</data>
		<key>Resources/img/icons/xxx.png</key>
		<data>
		nWqWQE/6RLXgjcE53X1jr7QTYzg=
		</data>
		<key>Resources/img/panda.icns</key>
		<data>
		IbLGCBrdrS+78zbZMTa9U0dm6CE=
		</data>
		<key>Resources/img/shutdown.png</key>
		<data>
		K0UjnvC4DqC907tryPgh+wfMy/0=
		</data>
		<key>Resources/img/sysinfo.icns</key>
		<data>
		Ltqqxr6FZCqK7Arl2g1zyZe3mCQ=
		</data>
		<key>Resources/img/vsmactool.icns</key>
		<data>
		UIGK19EkVFbwh6R4xSOUtwVzpBQ=
		</data>
		<key>Resources/keyring-25.6.0.dist-info/INSTALLER</key>
		<data>
		16AxQdXWseiLa1nvCLZoHfISxZk=
		</data>
		<key>Resources/keyring-25.6.0.dist-info/LICENSE</key>
		<data>
		BEXtD2mRDuruA28Jo5oTxuHzfhI=
		</data>
		<key>Resources/keyring-25.6.0.dist-info/METADATA</key>
		<data>
		WRVfQD62iIV68exEpB8/CT8tAzw=
		</data>
		<key>Resources/keyring-25.6.0.dist-info/RECORD</key>
		<data>
		OGtH7b3KCRBc1HhRUIuONXX2ino=
		</data>
		<key>Resources/keyring-25.6.0.dist-info/REQUESTED</key>
		<data>
		2jmj7l5rSw0yVb/vlWAYkK/YBwk=
		</data>
		<key>Resources/keyring-25.6.0.dist-info/WHEEL</key>
		<data>
		8dH5Vh360Fue0WnPyApHzleku2o=
		</data>
		<key>Resources/keyring-25.6.0.dist-info/entry_points.txt</key>
		<data>
		iRVBKy2hRNmOZD53OXV7DrKY+sc=
		</data>
		<key>Resources/keyring-25.6.0.dist-info/top_level.txt</key>
		<data>
		+Bx3AhWqVcspD92h48yte3axJNs=
		</data>
		<key>Resources/pillow-11.3.0.dist-info/INSTALLER</key>
		<data>
		16AxQdXWseiLa1nvCLZoHfISxZk=
		</data>
		<key>Resources/pillow-11.3.0.dist-info/METADATA</key>
		<data>
		z6MKn2s1fiAfui80x08JPx6vUL0=
		</data>
		<key>Resources/pillow-11.3.0.dist-info/RECORD</key>
		<data>
		l0pSWFsrL2wrDKzzJwAYvniFsvM=
		</data>
		<key>Resources/pillow-11.3.0.dist-info/REQUESTED</key>
		<data>
		2jmj7l5rSw0yVb/vlWAYkK/YBwk=
		</data>
		<key>Resources/pillow-11.3.0.dist-info/WHEEL</key>
		<data>
		SJl9F6/aJY0IXrQDCjGw9VI2sgI=
		</data>
		<key>Resources/pillow-11.3.0.dist-info/licenses/LICENSE</key>
		<data>
		IBgKaJx/AtQemcZy5qmM/jo38ZY=
		</data>
		<key>Resources/pillow-11.3.0.dist-info/top_level.txt</key>
		<data>
		eHTytqkSH7SHModU9bTFIeL7fpQ=
		</data>
		<key>Resources/pillow-11.3.0.dist-info/zip-safe</key>
		<data>
		rcg7GeeTSRscbqD9i0bNnzLlkvw=
		</data>
		<key>Resources/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/INSTALLER</key>
		<data>
		16AxQdXWseiLa1nvCLZoHfISxZk=
		</data>
		<key>Resources/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/LICENSE</key>
		<data>
		K4uBUimqimHkg/tLoFiLi2xJGJA=
		</data>
		<key>Resources/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/METADATA</key>
		<data>
		6Mgw2LCUIwDHyHs7j9FeoTluB70=
		</data>
		<key>Resources/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/RECORD</key>
		<data>
		s5THrsFYNQuvZ2rjGXvvTXFYsxw=
		</data>
		<key>Resources/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/REQUESTED</key>
		<data>
		2jmj7l5rSw0yVb/vlWAYkK/YBwk=
		</data>
		<key>Resources/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/WHEEL</key>
		<data>
		eCm0Mku1QnmUlBMaJw7Dva1N7e8=
		</data>
		<key>Resources/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/top_level.txt</key>
		<data>
		TmH5Jk3nR4O1kkJJvP4bBvF4ua0=
		</data>
		<key>Resources/setuptools/_vendor/jaraco/text/Lorem ipsum.txt</key>
		<data>
		eDY2ciZNnNP3LVwdNmXhZXsaUHE=
		</data>
		<key>Resources/tcl8/8.4/platform-1.0.19.tm</key>
		<data>
		YdK+5LeSYCeDUJi5lbT1zNrO1NU=
		</data>
		<key>Resources/tcl8/8.4/platform/shell-1.1.4.tm</key>
		<data>
		U3+wEz8cvVA4oE+m5N6x4Yi+Ih4=
		</data>
		<key>Resources/tcl8/8.5/msgcat-1.6.1.tm</key>
		<data>
		fCszIWqED2pr973Gzjo+PX6em64=
		</data>
		<key>Resources/tcl8/8.5/tcltest-2.5.9.tm</key>
		<data>
		rXyZWk5SH90mbh4m01YIZsAKPXc=
		</data>
		<key>Resources/tcl8/8.6/http-2.9.8.tm</key>
		<data>
		BGYeT0dfxJkObssb5uPqYMxlXYo=
		</data>
		<key>Resources/vsmactool.icns</key>
		<data>
		UIGK19EkVFbwh6R4xSOUtwVzpBQ=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Frameworks/PIL/.dylibs</key>
		<dict>
			<key>symlink</key>
			<string>__dot__dylibs</string>
		</dict>
		<key>Frameworks/PIL/AvifImagePlugin.py</key>
		<dict>
			<key>symlink</key>
			<string>../../Resources/PIL/AvifImagePlugin.py</string>
		</dict>
		<key>Frameworks/PIL/BdfFontFile.py</key>
		<dict>
			<key>symlink</key>
			<string>../../Resources/PIL/BdfFontFile.py</string>
		</dict>
		<key>Frameworks/PIL/BlpImagePlugin.py</key>
		<dict>
			<key>symlink</key>
			<string>../../Resources/PIL/BlpImagePlugin.py</string>
		</dict>
		<key>Frameworks/PIL/BmpImagePlugin.py</key>
		<dict>
			<key>symlink</key>
			<string>../../Resources/PIL/BmpImagePlugin.py</string>
		</dict>
		<key>Frameworks/PIL/BufrStubImagePlugin.py</key>
		<dict>
			<key>symlink</key>
			<string>../../Resources/PIL/BufrStubImagePlugin.py</string>
		</dict>
		<key>Frameworks/PIL/ContainerIO.py</key>
		<dict>
			<key>symlink</key>
			<string>../../Resources/PIL/ContainerIO.py</string>
		</dict>
		<key>Frameworks/PIL/CurImagePlugin.py</key>
		<dict>
			<key>symlink</key>
			<string>../../Resources/PIL/CurImagePlugin.py</string>
		</dict>
		<key>Frameworks/PIL/DcxImagePlugin.py</key>
		<dict>
			<key>symlink</key>
			<string>../../Resources/PIL/DcxImagePlugin.py</string>
		</dict>
		<key>Frameworks/PIL/DdsImagePlugin.py</key>
		<dict>
			<key>symlink</key>
			<string>../../Resources/PIL/DdsImagePlugin.py</string>
		</dict>
		<key>Frameworks/PIL/EpsImagePlugin.py</key>
		<dict>
			<key>symlink</key>
			<string>../../Resources/PIL/EpsImagePlugin.py</string>
		</dict>
		<key>Frameworks/PIL/ExifTags.py</key>
		<dict>
			<key>symlink</key>
			<string>../../Resources/PIL/ExifTags.py</string>
		</dict>
		<key>Frameworks/PIL/FitsImagePlugin.py</key>
		<dict>
			<key>symlink</key>
			<string>../../Resources/PIL/FitsImagePlugin.py</string>
		</dict>
		<key>Frameworks/PIL/FliImagePlugin.py</key>
		<dict>
			<key>symlink</key>
			<string>../../Resources/PIL/FliImagePlugin.py</string>
		</dict>
		<key>Frameworks/PIL/FontFile.py</key>
		<dict>
			<key>symlink</key>
			<string>../../Resources/PIL/FontFile.py</string>
		</dict>
		<key>Frameworks/PIL/FpxImagePlugin.py</key>
		<dict>
			<key>symlink</key>
			<string>../../Resources/PIL/FpxImagePlugin.py</string>
		</dict>
		<key>Frameworks/PIL/FtexImagePlugin.py</key>
		<dict>
			<key>symlink</key>
			<string>../../Resources/PIL/FtexImagePlugin.py</string>
		</dict>
		<key>Frameworks/PIL/GbrImagePlugin.py</key>
		<dict>
			<key>symlink</key>
			<string>../../Resources/PIL/GbrImagePlugin.py</string>
		</dict>
		<key>Frameworks/PIL/GdImageFile.py</key>
		<dict>
			<key>symlink</key>
			<string>../../Resources/PIL/GdImageFile.py</string>
		</dict>
		<key>Frameworks/PIL/GifImagePlugin.py</key>
		<dict>
			<key>symlink</key>
			<string>../../Resources/PIL/GifImagePlugin.py</string>
		</dict>
		<key>Frameworks/PIL/GimpGradientFile.py</key>
		<dict>
			<key>symlink</key>
			<string>../../Resources/PIL/GimpGradientFile.py</string>
		</dict>
		<key>Frameworks/PIL/GimpPaletteFile.py</key>
		<dict>
			<key>symlink</key>
			<string>../../Resources/PIL/GimpPaletteFile.py</string>
		</dict>
		<key>Frameworks/PIL/GribStubImagePlugin.py</key>
		<dict>
			<key>symlink</key>
			<string>../../Resources/PIL/GribStubImagePlugin.py</string>
		</dict>
		<key>Frameworks/PIL/Hdf5StubImagePlugin.py</key>
		<dict>
			<key>symlink</key>
			<string>../../Resources/PIL/Hdf5StubImagePlugin.py</string>
		</dict>
		<key>Frameworks/PIL/IcnsImagePlugin.py</key>
		<dict>
			<key>symlink</key>
			<string>../../Resources/PIL/IcnsImagePlugin.py</string>
		</dict>
		<key>Frameworks/PIL/IcoImagePlugin.py</key>
		<dict>
			<key>symlink</key>
			<string>../../Resources/PIL/IcoImagePlugin.py</string>
		</dict>
		<key>Frameworks/PIL/ImImagePlugin.py</key>
		<dict>
			<key>symlink</key>
			<string>../../Resources/PIL/ImImagePlugin.py</string>
		</dict>
		<key>Frameworks/PIL/Image.py</key>
		<dict>
			<key>symlink</key>
			<string>../../Resources/PIL/Image.py</string>
		</dict>
		<key>Frameworks/PIL/ImageChops.py</key>
		<dict>
			<key>symlink</key>
			<string>../../Resources/PIL/ImageChops.py</string>
		</dict>
		<key>Frameworks/PIL/ImageCms.py</key>
		<dict>
			<key>symlink</key>
			<string>../../Resources/PIL/ImageCms.py</string>
		</dict>
		<key>Frameworks/PIL/ImageColor.py</key>
		<dict>
			<key>symlink</key>
			<string>../../Resources/PIL/ImageColor.py</string>
		</dict>
		<key>Frameworks/PIL/ImageDraw.py</key>
		<dict>
			<key>symlink</key>
			<string>../../Resources/PIL/ImageDraw.py</string>
		</dict>
		<key>Frameworks/PIL/ImageDraw2.py</key>
		<dict>
			<key>symlink</key>
			<string>../../Resources/PIL/ImageDraw2.py</string>
		</dict>
		<key>Frameworks/PIL/ImageEnhance.py</key>
		<dict>
			<key>symlink</key>
			<string>../../Resources/PIL/ImageEnhance.py</string>
		</dict>
		<key>Frameworks/PIL/ImageFile.py</key>
		<dict>
			<key>symlink</key>
			<string>../../Resources/PIL/ImageFile.py</string>
		</dict>
		<key>Frameworks/PIL/ImageFilter.py</key>
		<dict>
			<key>symlink</key>
			<string>../../Resources/PIL/ImageFilter.py</string>
		</dict>
		<key>Frameworks/PIL/ImageFont.py</key>
		<dict>
			<key>symlink</key>
			<string>../../Resources/PIL/ImageFont.py</string>
		</dict>
		<key>Frameworks/PIL/ImageGrab.py</key>
		<dict>
			<key>symlink</key>
			<string>../../Resources/PIL/ImageGrab.py</string>
		</dict>
		<key>Frameworks/PIL/ImageMath.py</key>
		<dict>
			<key>symlink</key>
			<string>../../Resources/PIL/ImageMath.py</string>
		</dict>
		<key>Frameworks/PIL/ImageMode.py</key>
		<dict>
			<key>symlink</key>
			<string>../../Resources/PIL/ImageMode.py</string>
		</dict>
		<key>Frameworks/PIL/ImageMorph.py</key>
		<dict>
			<key>symlink</key>
			<string>../../Resources/PIL/ImageMorph.py</string>
		</dict>
		<key>Frameworks/PIL/ImageOps.py</key>
		<dict>
			<key>symlink</key>
			<string>../../Resources/PIL/ImageOps.py</string>
		</dict>
		<key>Frameworks/PIL/ImagePalette.py</key>
		<dict>
			<key>symlink</key>
			<string>../../Resources/PIL/ImagePalette.py</string>
		</dict>
		<key>Frameworks/PIL/ImagePath.py</key>
		<dict>
			<key>symlink</key>
			<string>../../Resources/PIL/ImagePath.py</string>
		</dict>
		<key>Frameworks/PIL/ImageQt.py</key>
		<dict>
			<key>symlink</key>
			<string>../../Resources/PIL/ImageQt.py</string>
		</dict>
		<key>Frameworks/PIL/ImageSequence.py</key>
		<dict>
			<key>symlink</key>
			<string>../../Resources/PIL/ImageSequence.py</string>
		</dict>
		<key>Frameworks/PIL/ImageShow.py</key>
		<dict>
			<key>symlink</key>
			<string>../../Resources/PIL/ImageShow.py</string>
		</dict>
		<key>Frameworks/PIL/ImageStat.py</key>
		<dict>
			<key>symlink</key>
			<string>../../Resources/PIL/ImageStat.py</string>
		</dict>
		<key>Frameworks/PIL/ImageTk.py</key>
		<dict>
			<key>symlink</key>
			<string>../../Resources/PIL/ImageTk.py</string>
		</dict>
		<key>Frameworks/PIL/ImageTransform.py</key>
		<dict>
			<key>symlink</key>
			<string>../../Resources/PIL/ImageTransform.py</string>
		</dict>
		<key>Frameworks/PIL/ImageWin.py</key>
		<dict>
			<key>symlink</key>
			<string>../../Resources/PIL/ImageWin.py</string>
		</dict>
		<key>Frameworks/PIL/ImtImagePlugin.py</key>
		<dict>
			<key>symlink</key>
			<string>../../Resources/PIL/ImtImagePlugin.py</string>
		</dict>
		<key>Frameworks/PIL/IptcImagePlugin.py</key>
		<dict>
			<key>symlink</key>
			<string>../../Resources/PIL/IptcImagePlugin.py</string>
		</dict>
		<key>Frameworks/PIL/Jpeg2KImagePlugin.py</key>
		<dict>
			<key>symlink</key>
			<string>../../Resources/PIL/Jpeg2KImagePlugin.py</string>
		</dict>
		<key>Frameworks/PIL/JpegImagePlugin.py</key>
		<dict>
			<key>symlink</key>
			<string>../../Resources/PIL/JpegImagePlugin.py</string>
		</dict>
		<key>Frameworks/PIL/JpegPresets.py</key>
		<dict>
			<key>symlink</key>
			<string>../../Resources/PIL/JpegPresets.py</string>
		</dict>
		<key>Frameworks/PIL/McIdasImagePlugin.py</key>
		<dict>
			<key>symlink</key>
			<string>../../Resources/PIL/McIdasImagePlugin.py</string>
		</dict>
		<key>Frameworks/PIL/MicImagePlugin.py</key>
		<dict>
			<key>symlink</key>
			<string>../../Resources/PIL/MicImagePlugin.py</string>
		</dict>
		<key>Frameworks/PIL/MpegImagePlugin.py</key>
		<dict>
			<key>symlink</key>
			<string>../../Resources/PIL/MpegImagePlugin.py</string>
		</dict>
		<key>Frameworks/PIL/MpoImagePlugin.py</key>
		<dict>
			<key>symlink</key>
			<string>../../Resources/PIL/MpoImagePlugin.py</string>
		</dict>
		<key>Frameworks/PIL/MspImagePlugin.py</key>
		<dict>
			<key>symlink</key>
			<string>../../Resources/PIL/MspImagePlugin.py</string>
		</dict>
		<key>Frameworks/PIL/PSDraw.py</key>
		<dict>
			<key>symlink</key>
			<string>../../Resources/PIL/PSDraw.py</string>
		</dict>
		<key>Frameworks/PIL/PaletteFile.py</key>
		<dict>
			<key>symlink</key>
			<string>../../Resources/PIL/PaletteFile.py</string>
		</dict>
		<key>Frameworks/PIL/PalmImagePlugin.py</key>
		<dict>
			<key>symlink</key>
			<string>../../Resources/PIL/PalmImagePlugin.py</string>
		</dict>
		<key>Frameworks/PIL/PcdImagePlugin.py</key>
		<dict>
			<key>symlink</key>
			<string>../../Resources/PIL/PcdImagePlugin.py</string>
		</dict>
		<key>Frameworks/PIL/PcfFontFile.py</key>
		<dict>
			<key>symlink</key>
			<string>../../Resources/PIL/PcfFontFile.py</string>
		</dict>
		<key>Frameworks/PIL/PcxImagePlugin.py</key>
		<dict>
			<key>symlink</key>
			<string>../../Resources/PIL/PcxImagePlugin.py</string>
		</dict>
		<key>Frameworks/PIL/PdfImagePlugin.py</key>
		<dict>
			<key>symlink</key>
			<string>../../Resources/PIL/PdfImagePlugin.py</string>
		</dict>
		<key>Frameworks/PIL/PdfParser.py</key>
		<dict>
			<key>symlink</key>
			<string>../../Resources/PIL/PdfParser.py</string>
		</dict>
		<key>Frameworks/PIL/PixarImagePlugin.py</key>
		<dict>
			<key>symlink</key>
			<string>../../Resources/PIL/PixarImagePlugin.py</string>
		</dict>
		<key>Frameworks/PIL/PngImagePlugin.py</key>
		<dict>
			<key>symlink</key>
			<string>../../Resources/PIL/PngImagePlugin.py</string>
		</dict>
		<key>Frameworks/PIL/PpmImagePlugin.py</key>
		<dict>
			<key>symlink</key>
			<string>../../Resources/PIL/PpmImagePlugin.py</string>
		</dict>
		<key>Frameworks/PIL/PsdImagePlugin.py</key>
		<dict>
			<key>symlink</key>
			<string>../../Resources/PIL/PsdImagePlugin.py</string>
		</dict>
		<key>Frameworks/PIL/QoiImagePlugin.py</key>
		<dict>
			<key>symlink</key>
			<string>../../Resources/PIL/QoiImagePlugin.py</string>
		</dict>
		<key>Frameworks/PIL/SgiImagePlugin.py</key>
		<dict>
			<key>symlink</key>
			<string>../../Resources/PIL/SgiImagePlugin.py</string>
		</dict>
		<key>Frameworks/PIL/SpiderImagePlugin.py</key>
		<dict>
			<key>symlink</key>
			<string>../../Resources/PIL/SpiderImagePlugin.py</string>
		</dict>
		<key>Frameworks/PIL/SunImagePlugin.py</key>
		<dict>
			<key>symlink</key>
			<string>../../Resources/PIL/SunImagePlugin.py</string>
		</dict>
		<key>Frameworks/PIL/TarIO.py</key>
		<dict>
			<key>symlink</key>
			<string>../../Resources/PIL/TarIO.py</string>
		</dict>
		<key>Frameworks/PIL/TgaImagePlugin.py</key>
		<dict>
			<key>symlink</key>
			<string>../../Resources/PIL/TgaImagePlugin.py</string>
		</dict>
		<key>Frameworks/PIL/TiffImagePlugin.py</key>
		<dict>
			<key>symlink</key>
			<string>../../Resources/PIL/TiffImagePlugin.py</string>
		</dict>
		<key>Frameworks/PIL/TiffTags.py</key>
		<dict>
			<key>symlink</key>
			<string>../../Resources/PIL/TiffTags.py</string>
		</dict>
		<key>Frameworks/PIL/WalImageFile.py</key>
		<dict>
			<key>symlink</key>
			<string>../../Resources/PIL/WalImageFile.py</string>
		</dict>
		<key>Frameworks/PIL/WebPImagePlugin.py</key>
		<dict>
			<key>symlink</key>
			<string>../../Resources/PIL/WebPImagePlugin.py</string>
		</dict>
		<key>Frameworks/PIL/WmfImagePlugin.py</key>
		<dict>
			<key>symlink</key>
			<string>../../Resources/PIL/WmfImagePlugin.py</string>
		</dict>
		<key>Frameworks/PIL/XVThumbImagePlugin.py</key>
		<dict>
			<key>symlink</key>
			<string>../../Resources/PIL/XVThumbImagePlugin.py</string>
		</dict>
		<key>Frameworks/PIL/XbmImagePlugin.py</key>
		<dict>
			<key>symlink</key>
			<string>../../Resources/PIL/XbmImagePlugin.py</string>
		</dict>
		<key>Frameworks/PIL/XpmImagePlugin.py</key>
		<dict>
			<key>symlink</key>
			<string>../../Resources/PIL/XpmImagePlugin.py</string>
		</dict>
		<key>Frameworks/PIL/__dot__dylibs/libXau.6.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			TBg8IqIK8KaDQKHeZcNDwEWK8BA=
			</data>
			<key>requirement</key>
			<string>cdhash H"4c183c22a20af0a68340a1de65c343c0458af010"</string>
		</dict>
		<key>Frameworks/PIL/__dot__dylibs/libavif.16.3.0.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			X1bHEnCm7vGLD28dbtzaatMJZv0=
			</data>
			<key>requirement</key>
			<string>cdhash H"5f56c71270a6eef18b0f6f1d6edcda6ad30966fd"</string>
		</dict>
		<key>Frameworks/PIL/__dot__dylibs/libbrotlicommon.1.1.0.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			pYMO9eqL0XlGyI2Z9S5qNVccCG0=
			</data>
			<key>requirement</key>
			<string>cdhash H"a5830ef5ea8bd17946c88d99f52e6a35571c086d"</string>
		</dict>
		<key>Frameworks/PIL/__dot__dylibs/libbrotlidec.1.1.0.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			lgR9qTmDuZYYgl8o7088DnQu5IY=
			</data>
			<key>requirement</key>
			<string>cdhash H"96047da93983b99618825f28ef4f3c0e742ee486"</string>
		</dict>
		<key>Frameworks/PIL/__dot__dylibs/libfreetype.6.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			G3bFRFyYfk795jOgWbegoAs+X9U=
			</data>
			<key>requirement</key>
			<string>cdhash H"1b76c5445c987e4efde633a059b7a0a00b3e5fd5"</string>
		</dict>
		<key>Frameworks/PIL/__dot__dylibs/libharfbuzz.0.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			oUy1jxummTkfEaVdV6bFaSCM14E=
			</data>
			<key>requirement</key>
			<string>cdhash H"a14cb58f1ba699391f11a55d57a6c569208cd781"</string>
		</dict>
		<key>Frameworks/PIL/__dot__dylibs/libjpeg.62.4.0.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			St6hFrRbH16InEuX5UFIa25G+U0=
			</data>
			<key>requirement</key>
			<string>cdhash H"4adea116b45b1f5e889c4b97e541486b6e46f94d"</string>
		</dict>
		<key>Frameworks/PIL/__dot__dylibs/liblcms2.2.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			hSkDY7TVbRvpUdMRqCAK7fKrNOQ=
			</data>
			<key>requirement</key>
			<string>cdhash H"85290363b4d56d1be951d311a8200aedf2ab34e4"</string>
		</dict>
		<key>Frameworks/PIL/__dot__dylibs/liblzma.5.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			y6XLkyMMMlO5Kj0wuzHW+Fso3cU=
			</data>
			<key>requirement</key>
			<string>cdhash H"cba5cb93230c3253b92a3d30bb31d6f85b28ddc5"</string>
		</dict>
		<key>Frameworks/PIL/__dot__dylibs/libopenjp2.2.5.3.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			9lOhbLZ9eT4kg87COu8HnC74omU=
			</data>
			<key>requirement</key>
			<string>cdhash H"f653a16cb67d793e2483cec23aef079c2ef8a265"</string>
		</dict>
		<key>Frameworks/PIL/__dot__dylibs/libpng16.16.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			NMBQXZMcKoqqEgsq9QISMx4Tpao=
			</data>
			<key>requirement</key>
			<string>cdhash H"34c0505d931c2a8aaa120b2af50212331e13a5aa"</string>
		</dict>
		<key>Frameworks/PIL/__dot__dylibs/libsharpyuv.0.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			GuevMIxEaac8zKewIk8tys5NDHA=
			</data>
			<key>requirement</key>
			<string>cdhash H"1ae7af308c4469a73ccca7b0224f2dcace4d0c70"</string>
		</dict>
		<key>Frameworks/PIL/__dot__dylibs/libtiff.6.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			7I5Ubb1Tcmf0SDpYZYqlNJqOiUk=
			</data>
			<key>requirement</key>
			<string>cdhash H"ec8e546dbd537267f4483a58658aa5349a8e8949"</string>
		</dict>
		<key>Frameworks/PIL/__dot__dylibs/libwebp.7.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			Y0dU7sYINzlqBibU+HZIvDCHbNo=
			</data>
			<key>requirement</key>
			<string>cdhash H"634754eec60837396a0626d4f87648bc30876cda"</string>
		</dict>
		<key>Frameworks/PIL/__dot__dylibs/libwebpdemux.2.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			BiiB3gOroC6jQYa+Wrl2WDnY1Lo=
			</data>
			<key>requirement</key>
			<string>cdhash H"062881de03aba02ea34186be5ab9765839d8d4ba"</string>
		</dict>
		<key>Frameworks/PIL/__dot__dylibs/libwebpmux.3.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			QdLeHnP4nLT6QgPxoDzo9sM8PqU=
			</data>
			<key>requirement</key>
			<string>cdhash H"41d2de1e73f89cb4fa4203f1a03ce8f6c33c3ea5"</string>
		</dict>
		<key>Frameworks/PIL/__dot__dylibs/libxcb.1.1.0.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			tYvB/JLs2nTp6/CBaSmo0LcGAnE=
			</data>
			<key>requirement</key>
			<string>cdhash H"b58bc1fc92ecda74e9ebf0816929a8d0b7060271"</string>
		</dict>
		<key>Frameworks/PIL/__dot__dylibs/libz.1.3.1.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			387fhoteKWP9iiiUT3HvIAaIawM=
			</data>
			<key>requirement</key>
			<string>cdhash H"dfcedf868b5e2963fd8a28944f71ef2006886b03"</string>
		</dict>
		<key>Frameworks/PIL/__init__.py</key>
		<dict>
			<key>symlink</key>
			<string>../../Resources/PIL/__init__.py</string>
		</dict>
		<key>Frameworks/PIL/__main__.py</key>
		<dict>
			<key>symlink</key>
			<string>../../Resources/PIL/__main__.py</string>
		</dict>
		<key>Frameworks/PIL/_avif.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			NR+ehLt7/FZESfhTInytuxtwKtU=
			</data>
			<key>requirement</key>
			<string>cdhash H"351f9e84bb7bfc564449f853227cadbb1b702ad5"</string>
		</dict>
		<key>Frameworks/PIL/_avif.pyi</key>
		<dict>
			<key>symlink</key>
			<string>../../Resources/PIL/_avif.pyi</string>
		</dict>
		<key>Frameworks/PIL/_binary.py</key>
		<dict>
			<key>symlink</key>
			<string>../../Resources/PIL/_binary.py</string>
		</dict>
		<key>Frameworks/PIL/_deprecate.py</key>
		<dict>
			<key>symlink</key>
			<string>../../Resources/PIL/_deprecate.py</string>
		</dict>
		<key>Frameworks/PIL/_imaging.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			oOt2XoEJ2DyNJfSVTmuOKKM8yyw=
			</data>
			<key>requirement</key>
			<string>cdhash H"a0eb765e8109d83c8d25f4954e6b8e28a33ccb2c"</string>
		</dict>
		<key>Frameworks/PIL/_imaging.pyi</key>
		<dict>
			<key>symlink</key>
			<string>../../Resources/PIL/_imaging.pyi</string>
		</dict>
		<key>Frameworks/PIL/_imagingcms.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			Y11AmbqpsL4JPyqW7YLJlQclv4k=
			</data>
			<key>requirement</key>
			<string>cdhash H"635d4099baa9b0be093f2a96ed82c9950725bf89"</string>
		</dict>
		<key>Frameworks/PIL/_imagingcms.pyi</key>
		<dict>
			<key>symlink</key>
			<string>../../Resources/PIL/_imagingcms.pyi</string>
		</dict>
		<key>Frameworks/PIL/_imagingft.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			A2rXGSA0IPgX2cbZjD8rV9gQS6E=
			</data>
			<key>requirement</key>
			<string>cdhash H"036ad719203420f817d9c6d98c3f2b57d8104ba1"</string>
		</dict>
		<key>Frameworks/PIL/_imagingft.pyi</key>
		<dict>
			<key>symlink</key>
			<string>../../Resources/PIL/_imagingft.pyi</string>
		</dict>
		<key>Frameworks/PIL/_imagingmath.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			FhLQOL+rOdulGO9V6Ta/yg0DZdE=
			</data>
			<key>requirement</key>
			<string>cdhash H"1612d038bfab39dba518ef55e936bfca0d0365d1"</string>
		</dict>
		<key>Frameworks/PIL/_imagingmath.pyi</key>
		<dict>
			<key>symlink</key>
			<string>../../Resources/PIL/_imagingmath.pyi</string>
		</dict>
		<key>Frameworks/PIL/_imagingmorph.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			7ocgdbMPIdXWwcerMBV3VQT0xTg=
			</data>
			<key>requirement</key>
			<string>cdhash H"ee872075b30f21d5d6c1c7ab3015775504f4c538"</string>
		</dict>
		<key>Frameworks/PIL/_imagingmorph.pyi</key>
		<dict>
			<key>symlink</key>
			<string>../../Resources/PIL/_imagingmorph.pyi</string>
		</dict>
		<key>Frameworks/PIL/_imagingtk.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			f4wW+zPJ25Ghb/n1Q8ifaHW/wcY=
			</data>
			<key>requirement</key>
			<string>cdhash H"7f8c16fb33c9db91a16ff9f543c89f6875bfc1c6"</string>
		</dict>
		<key>Frameworks/PIL/_imagingtk.pyi</key>
		<dict>
			<key>symlink</key>
			<string>../../Resources/PIL/_imagingtk.pyi</string>
		</dict>
		<key>Frameworks/PIL/_tkinter_finder.py</key>
		<dict>
			<key>symlink</key>
			<string>../../Resources/PIL/_tkinter_finder.py</string>
		</dict>
		<key>Frameworks/PIL/_typing.py</key>
		<dict>
			<key>symlink</key>
			<string>../../Resources/PIL/_typing.py</string>
		</dict>
		<key>Frameworks/PIL/_util.py</key>
		<dict>
			<key>symlink</key>
			<string>../../Resources/PIL/_util.py</string>
		</dict>
		<key>Frameworks/PIL/_version.py</key>
		<dict>
			<key>symlink</key>
			<string>../../Resources/PIL/_version.py</string>
		</dict>
		<key>Frameworks/PIL/_webp.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			64ijkQuLEMbUqT6cpbP3PLCe/q8=
			</data>
			<key>requirement</key>
			<string>cdhash H"eb88a3910b8b10c6d4a93e9ca5b3f73cb09efeaf"</string>
		</dict>
		<key>Frameworks/PIL/_webp.pyi</key>
		<dict>
			<key>symlink</key>
			<string>../../Resources/PIL/_webp.pyi</string>
		</dict>
		<key>Frameworks/PIL/features.py</key>
		<dict>
			<key>symlink</key>
			<string>../../Resources/PIL/features.py</string>
		</dict>
		<key>Frameworks/PIL/py.typed</key>
		<dict>
			<key>symlink</key>
			<string>../../Resources/PIL/py.typed</string>
		</dict>
		<key>Frameworks/PIL/report.py</key>
		<dict>
			<key>symlink</key>
			<string>../../Resources/PIL/report.py</string>
		</dict>
		<key>Frameworks/Python</key>
		<dict>
			<key>symlink</key>
			<string>Python.framework/Versions/3.13/Python</string>
		</dict>
		<key>Frameworks/Python.framework</key>
		<dict>
			<key>cdhash</key>
			<data>
			IPox3u+KtNhr9eNFi8aiNLMghEo=
			</data>
			<key>requirement</key>
			<string>cdhash H"20fa31deef8ab4d86bf5e3458bc6a234b320844a"</string>
		</dict>
		<key>Frameworks/Tcl</key>
		<dict>
			<key>cdhash</key>
			<data>
			3kmSq4FsQoHtxQDmDYwSGeko79g=
			</data>
			<key>requirement</key>
			<string>cdhash H"de4992ab816c4281edc500e60d8c1219e928efd8"</string>
		</dict>
		<key>Frameworks/Tk</key>
		<dict>
			<key>cdhash</key>
			<data>
			uY8vGREG4fUHPntR89BAZdqgHA8=
			</data>
			<key>requirement</key>
			<string>cdhash H"b98f2f191106e1f5073e7b51f3d04065daa01c0f"</string>
		</dict>
		<key>Frameworks/_tcl_data</key>
		<dict>
			<key>symlink</key>
			<string>../Resources/_tcl_data</string>
		</dict>
		<key>Frameworks/_tk_data</key>
		<dict>
			<key>symlink</key>
			<string>../Resources/_tk_data</string>
		</dict>
		<key>Frameworks/base_library.zip</key>
		<dict>
			<key>symlink</key>
			<string>../Resources/base_library.zip</string>
		</dict>
		<key>Frameworks/customtkinter</key>
		<dict>
			<key>symlink</key>
			<string>../Resources/customtkinter</string>
		</dict>
		<key>Frameworks/customtkinter-5.2.2.dist-info</key>
		<dict>
			<key>symlink</key>
			<string>../Resources/customtkinter-5.2.2.dist-info</string>
		</dict>
		<key>Frameworks/img</key>
		<dict>
			<key>symlink</key>
			<string>../Resources/img</string>
		</dict>
		<key>Frameworks/keyring-25.6.0.dist-info</key>
		<dict>
			<key>symlink</key>
			<string>../Resources/keyring-25.6.0.dist-info</string>
		</dict>
		<key>Frameworks/lib-dynload/_asyncio.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			lGwFu8e04z4/MDQ5LKj4PZW90Rg=
			</data>
			<key>requirement</key>
			<string>cdhash H"946c05bbc7b4e33e3f3034392ca8f83d95bdd118"</string>
		</dict>
		<key>Frameworks/lib-dynload/_bisect.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			ns9cDDAQ15lLX8fr897F8qvZ1Ag=
			</data>
			<key>requirement</key>
			<string>cdhash H"9ecf5c0c3010d7994b5fc7ebf3dec5f2abd9d408"</string>
		</dict>
		<key>Frameworks/lib-dynload/_blake2.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			ptiKHv0lx2jpztW1r0Am2jM1YNg=
			</data>
			<key>requirement</key>
			<string>cdhash H"a6d88a1efd25c768e9ced5b5af4026da333560d8"</string>
		</dict>
		<key>Frameworks/lib-dynload/_bz2.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			ia5GfedI/QxMFHBTA7jk5zoPXNE=
			</data>
			<key>requirement</key>
			<string>cdhash H"89ae467de748fd0c4c14705303b8e4e73a0f5cd1"</string>
		</dict>
		<key>Frameworks/lib-dynload/_codecs_cn.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			WKiK1IarhxGN7ODWS2HTI8LjE4s=
			</data>
			<key>requirement</key>
			<string>cdhash H"58a88ad486ab87118dece0d64b61d323c2e3138b"</string>
		</dict>
		<key>Frameworks/lib-dynload/_codecs_hk.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			ESj5iRDKWtgA1B3/cNLCWaFvf0A=
			</data>
			<key>requirement</key>
			<string>cdhash H"1128f98910ca5ad800d41dff70d2c259a16f7f40"</string>
		</dict>
		<key>Frameworks/lib-dynload/_codecs_iso2022.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			GfeI4rxY6+mWP7R5VkODeFTQgGE=
			</data>
			<key>requirement</key>
			<string>cdhash H"19f788e2bc58ebe9963fb4795643837854d08061"</string>
		</dict>
		<key>Frameworks/lib-dynload/_codecs_jp.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			1OXLEpuoWNa/Zq3r8EXIzMNLjlo=
			</data>
			<key>requirement</key>
			<string>cdhash H"d4e5cb129ba858d6bf66adebf045c8ccc34b8e5a"</string>
		</dict>
		<key>Frameworks/lib-dynload/_codecs_kr.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			F42hhSh/TlE/LSQL0bvfDwiT5PA=
			</data>
			<key>requirement</key>
			<string>cdhash H"178da185287f4e513f2d240bd1bbdf0f0893e4f0"</string>
		</dict>
		<key>Frameworks/lib-dynload/_codecs_tw.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			mKv0dJtS8pnhdCR51T/nDymtbt0=
			</data>
			<key>requirement</key>
			<string>cdhash H"98abf4749b52f299e1742479d53fe70f29ad6edd"</string>
		</dict>
		<key>Frameworks/lib-dynload/_contextvars.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			LVn+ARZoa7I76P5+iKKFMQSrAck=
			</data>
			<key>requirement</key>
			<string>cdhash H"2d59fe0116686bb23be8fe7e88a2853104ab01c9"</string>
		</dict>
		<key>Frameworks/lib-dynload/_csv.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			EHxx7N5IHIhAE8SQp1ZX+4nECX0=
			</data>
			<key>requirement</key>
			<string>cdhash H"107c71ecde481c884013c490a75657fb89c4097d"</string>
		</dict>
		<key>Frameworks/lib-dynload/_ctypes.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			kioM/YBl8jKLMkPRVRTaTNbRrdQ=
			</data>
			<key>requirement</key>
			<string>cdhash H"922a0cfd8065f2328b3243d15514da4cd6d1add4"</string>
		</dict>
		<key>Frameworks/lib-dynload/_curses.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			yDYWZ+SI//grwV0tQtamA63WkzM=
			</data>
			<key>requirement</key>
			<string>cdhash H"c8361667e488fff82bc15d2d42d6a603add69333"</string>
		</dict>
		<key>Frameworks/lib-dynload/_datetime.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			AQdG/keJ/ZlC6yHkJ9iNhINHVpk=
			</data>
			<key>requirement</key>
			<string>cdhash H"010746fe4789fd9942eb21e427d88d8483475699"</string>
		</dict>
		<key>Frameworks/lib-dynload/_decimal.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			40YJMrfeA1q1658zONE9xB+w2Vg=
			</data>
			<key>requirement</key>
			<string>cdhash H"e3460932b7de035ab5eb9f3338d13dc41fb0d958"</string>
		</dict>
		<key>Frameworks/lib-dynload/_elementtree.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			5DvDy9eClmDDqkj49NXR3end1rs=
			</data>
			<key>requirement</key>
			<string>cdhash H"e43bc3cbd7829660c3aa48f8f4d5d1dde9ddd6bb"</string>
		</dict>
		<key>Frameworks/lib-dynload/_hashlib.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			jHuXJrgm2IrCpMPmPMmJtPe77gY=
			</data>
			<key>requirement</key>
			<string>cdhash H"8c7b9726b826d88ac2a4c3e63cc989b4f7bbee06"</string>
		</dict>
		<key>Frameworks/lib-dynload/_heapq.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			1zqfOsuoSBxjGN/q9soRy2kRviA=
			</data>
			<key>requirement</key>
			<string>cdhash H"d73a9f3acba8481c6318dfeaf6ca11cb6911be20"</string>
		</dict>
		<key>Frameworks/lib-dynload/_json.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			1xHuMF4jHHA3RGqhQs1rbCLNTK8=
			</data>
			<key>requirement</key>
			<string>cdhash H"d711ee305e231c7037446aa142cd6b6c22cd4caf"</string>
		</dict>
		<key>Frameworks/lib-dynload/_lzma.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			PcOIilpcdoih9RhRcxRm21w+Amg=
			</data>
			<key>requirement</key>
			<string>cdhash H"3dc3888a5a5c7688a1f51851731466db5c3e0268"</string>
		</dict>
		<key>Frameworks/lib-dynload/_md5.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			VLFND8ci5KuE/w/lAoLy8moMXG0=
			</data>
			<key>requirement</key>
			<string>cdhash H"54b14d0fc722e4ab84ff0fe50282f2f26a0c5c6d"</string>
		</dict>
		<key>Frameworks/lib-dynload/_multibytecodec.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			0lnSFD/px99t+szi4wti3ytpi1U=
			</data>
			<key>requirement</key>
			<string>cdhash H"d259d2143fe9c7df6dfacce2e30b62df2b698b55"</string>
		</dict>
		<key>Frameworks/lib-dynload/_multiprocessing.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			u5UFIM1gW9sCKhs8rhclf9Vov9g=
			</data>
			<key>requirement</key>
			<string>cdhash H"bb950520cd605bdb022a1b3cae17257fd568bfd8"</string>
		</dict>
		<key>Frameworks/lib-dynload/_opcode.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			9QpCpIAObiFlXG2xOWroXLFfxi4=
			</data>
			<key>requirement</key>
			<string>cdhash H"f50a42a4800e6e21655c6db1396ae85cb15fc62e"</string>
		</dict>
		<key>Frameworks/lib-dynload/_pickle.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			cF37a6bypnr+VoLMyegBqmUoFkY=
			</data>
			<key>requirement</key>
			<string>cdhash H"705dfb6ba6f2a67afe5682ccc9e801aa65281646"</string>
		</dict>
		<key>Frameworks/lib-dynload/_posixshmem.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			u2EA4zRSU835KuLj1EHJQHUQKmA=
			</data>
			<key>requirement</key>
			<string>cdhash H"bb6100e3345253cdf92ae2e3d441c94075102a60"</string>
		</dict>
		<key>Frameworks/lib-dynload/_posixsubprocess.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			BDPyW4XPfVc5gaoj03GWr9MqGDY=
			</data>
			<key>requirement</key>
			<string>cdhash H"0433f25b85cf7d573981aa23d37196afd32a1836"</string>
		</dict>
		<key>Frameworks/lib-dynload/_queue.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			+VVTSyypGv5RdwYkBXCM3lj6hwQ=
			</data>
			<key>requirement</key>
			<string>cdhash H"f955534b2ca91afe5177062405708cde58fa8704"</string>
		</dict>
		<key>Frameworks/lib-dynload/_random.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			yB6kCEIeB1H06ONM7s/fvMYueag=
			</data>
			<key>requirement</key>
			<string>cdhash H"c81ea408421e0751f4e8e34ceecfdfbcc62e79a8"</string>
		</dict>
		<key>Frameworks/lib-dynload/_scproxy.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			57wayX7iGObPYbjMAO3OF0ljnZE=
			</data>
			<key>requirement</key>
			<string>cdhash H"e7bc1ac97ee218e6cf61b8cc00edce1749639d91"</string>
		</dict>
		<key>Frameworks/lib-dynload/_sha1.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			aJvs5LtvufCm2wY6KqOlVFGQaC8=
			</data>
			<key>requirement</key>
			<string>cdhash H"689bece4bb6fb9f0a6db063a2aa3a5545190682f"</string>
		</dict>
		<key>Frameworks/lib-dynload/_sha2.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			kofCw6fQ5nNLzJynyiqoS82DZyo=
			</data>
			<key>requirement</key>
			<string>cdhash H"9287c2c3a7d0e6734bcc9ca7ca2aa84bcd83672a"</string>
		</dict>
		<key>Frameworks/lib-dynload/_sha3.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			B+t2pCTGUfmlD1KXwnxqbQVz/Sw=
			</data>
			<key>requirement</key>
			<string>cdhash H"07eb76a424c651f9a50f5297c27c6a6d0573fd2c"</string>
		</dict>
		<key>Frameworks/lib-dynload/_socket.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			Zl9Cn1ABw7BnYDCFEf8cCBf/SXM=
			</data>
			<key>requirement</key>
			<string>cdhash H"665f429f5001c3b06760308511ff1c0817ff4973"</string>
		</dict>
		<key>Frameworks/lib-dynload/_ssl.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			6Wr8lZM3chEyd0aRWbX2wuE7myo=
			</data>
			<key>requirement</key>
			<string>cdhash H"e96afc95933772113277469159b5f6c2e13b9b2a"</string>
		</dict>
		<key>Frameworks/lib-dynload/_statistics.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			1+mc+pKF0NqggXnT1XVCOmZwauU=
			</data>
			<key>requirement</key>
			<string>cdhash H"d7e99cfa9285d0daa08179d3d575423a66706ae5"</string>
		</dict>
		<key>Frameworks/lib-dynload/_struct.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			3OEFiehyV3yk6I10ugWQCgRrP3Q=
			</data>
			<key>requirement</key>
			<string>cdhash H"dce10589e872577ca4e88d74ba05900a046b3f74"</string>
		</dict>
		<key>Frameworks/lib-dynload/_tkinter.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			k19h2haUb1JCUg3zZVfgzfKld9I=
			</data>
			<key>requirement</key>
			<string>cdhash H"935f61da16946f5242520df36557e0cdf2a577d2"</string>
		</dict>
		<key>Frameworks/lib-dynload/array.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			buPV/U+fHamxJhxwRExuKO1ixII=
			</data>
			<key>requirement</key>
			<string>cdhash H"6ee3d5fd4f9f1da9b1261c70444c6e28ed62c482"</string>
		</dict>
		<key>Frameworks/lib-dynload/binascii.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			wEYoHjH8BihPiriS/nvEl6Y/5x0=
			</data>
			<key>requirement</key>
			<string>cdhash H"c046281e31fc06284f8ab892fe7bc497a63fe71d"</string>
		</dict>
		<key>Frameworks/lib-dynload/fcntl.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			UfYvig3MSE8hpkG7zrD1OmR+EnI=
			</data>
			<key>requirement</key>
			<string>cdhash H"51f62f8a0dcc484f21a641bbceb0f53a647e1272"</string>
		</dict>
		<key>Frameworks/lib-dynload/grp.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			C/UaYKE8M3w6AxJmqro0B4Gb/YI=
			</data>
			<key>requirement</key>
			<string>cdhash H"0bf51a60a13c337c3a031266aaba3407819bfd82"</string>
		</dict>
		<key>Frameworks/lib-dynload/math.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			C0lQmQf6UEq9uUtmvGnfYitidrw=
			</data>
			<key>requirement</key>
			<string>cdhash H"0b49509907fa504abdb94b66bc69df622b6276bc"</string>
		</dict>
		<key>Frameworks/lib-dynload/mmap.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			2ar762x21XeTax+s30ge0YL6m7g=
			</data>
			<key>requirement</key>
			<string>cdhash H"d9aafbeb6c76d577936b1facdf481ed182fa9bb8"</string>
		</dict>
		<key>Frameworks/lib-dynload/pyexpat.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			P2BEUeDJra1rznf4rOW7xO42a4g=
			</data>
			<key>requirement</key>
			<string>cdhash H"3f604451e0c9adad6bce77f8ace5bbc4ee366b88"</string>
		</dict>
		<key>Frameworks/lib-dynload/readline.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			D42Nf6Bvo06RIO8Kx4MXUhztZ4Q=
			</data>
			<key>requirement</key>
			<string>cdhash H"0f8d8d7fa06fa34e9120ef0ac78317521ced6784"</string>
		</dict>
		<key>Frameworks/lib-dynload/resource.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			cGXsvSpkuMWrSlBvXmHXUeln7nI=
			</data>
			<key>requirement</key>
			<string>cdhash H"7065ecbd2a64b8c5ab4a506f5e61d751e967ee72"</string>
		</dict>
		<key>Frameworks/lib-dynload/select.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			rgkwTzHFocwCqYKbWdHPwaN9zh0=
			</data>
			<key>requirement</key>
			<string>cdhash H"ae09304f31c5a1cc02a9829b59d1cfc1a37dce1d"</string>
		</dict>
		<key>Frameworks/lib-dynload/syslog.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			rOT4SVwQa9ax2/d9DXuPoMwQ4Jo=
			</data>
			<key>requirement</key>
			<string>cdhash H"ace4f8495c106bd6b1dbf77d0d7b8fa0cc10e09a"</string>
		</dict>
		<key>Frameworks/lib-dynload/termios.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			MpQ+er172mniZ6Q21wHjk1l8n/Q=
			</data>
			<key>requirement</key>
			<string>cdhash H"32943e7abd7bda69e267a436d701e393597c9ff4"</string>
		</dict>
		<key>Frameworks/lib-dynload/unicodedata.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			Fy7CYi7doDwaOI6UFVt601UiU3Q=
			</data>
			<key>requirement</key>
			<string>cdhash H"172ec2622edda03c1a388e94155b7ad355225374"</string>
		</dict>
		<key>Frameworks/lib-dynload/zlib.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			NTH+avAHIdx93anFte1weBIC5gs=
			</data>
			<key>requirement</key>
			<string>cdhash H"3531fe6af00721dc7ddda9c5b5ed70781202e60b"</string>
		</dict>
		<key>Frameworks/libXau.6.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libXau.6.dylib</string>
		</dict>
		<key>Frameworks/libavif.16.3.0.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libavif.16.3.0.dylib</string>
		</dict>
		<key>Frameworks/libbrotlicommon.1.1.0.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libbrotlicommon.1.1.0.dylib</string>
		</dict>
		<key>Frameworks/libbrotlidec.1.1.0.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libbrotlidec.1.1.0.dylib</string>
		</dict>
		<key>Frameworks/libcrypto.3.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			Y3Is+vV3frHmx68TvHh6sOeM8cI=
			</data>
			<key>requirement</key>
			<string>cdhash H"63722cfaf5777eb1e6c7af13bc787ab0e78cf1c2"</string>
		</dict>
		<key>Frameworks/libfreetype.6.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libfreetype.6.dylib</string>
		</dict>
		<key>Frameworks/libharfbuzz.0.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libharfbuzz.0.dylib</string>
		</dict>
		<key>Frameworks/libjpeg.62.4.0.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libjpeg.62.4.0.dylib</string>
		</dict>
		<key>Frameworks/liblcms2.2.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/liblcms2.2.dylib</string>
		</dict>
		<key>Frameworks/liblzma.5.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/liblzma.5.dylib</string>
		</dict>
		<key>Frameworks/libncurses.6.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			UMUN9Z/xg92oC3EZ1WLRuqI1QFs=
			</data>
			<key>requirement</key>
			<string>cdhash H"50c50df59ff183dda80b7119d562d1baa235405b"</string>
		</dict>
		<key>Frameworks/libopenjp2.2.5.3.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libopenjp2.2.5.3.dylib</string>
		</dict>
		<key>Frameworks/libpng16.16.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libpng16.16.dylib</string>
		</dict>
		<key>Frameworks/libsharpyuv.0.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libsharpyuv.0.dylib</string>
		</dict>
		<key>Frameworks/libssl.3.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			Ld3xSdaT2h0YH2cr93uHEqrTKQc=
			</data>
			<key>requirement</key>
			<string>cdhash H"2dddf149d693da1d181f672bf77b8712aad32907"</string>
		</dict>
		<key>Frameworks/libtiff.6.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libtiff.6.dylib</string>
		</dict>
		<key>Frameworks/libwebp.7.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libwebp.7.dylib</string>
		</dict>
		<key>Frameworks/libwebpdemux.2.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libwebpdemux.2.dylib</string>
		</dict>
		<key>Frameworks/libwebpmux.3.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libwebpmux.3.dylib</string>
		</dict>
		<key>Frameworks/libxcb.1.1.0.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libxcb.1.1.0.dylib</string>
		</dict>
		<key>Frameworks/libz.1.3.1.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libz.1.3.1.dylib</string>
		</dict>
		<key>Frameworks/pillow-11.3.0.dist-info</key>
		<dict>
			<key>symlink</key>
			<string>../Resources/pillow-11.3.0.dist-info</string>
		</dict>
		<key>Frameworks/setuptools</key>
		<dict>
			<key>symlink</key>
			<string>../Resources/setuptools</string>
		</dict>
		<key>Frameworks/tcl8</key>
		<dict>
			<key>symlink</key>
			<string>../Resources/tcl8</string>
		</dict>
		<key>Resources/PIL/.dylibs</key>
		<dict>
			<key>symlink</key>
			<string>../../Frameworks/PIL/.dylibs</string>
		</dict>
		<key>Resources/PIL/AvifImagePlugin.py</key>
		<dict>
			<key>hash2</key>
			<data>
			5IiDMvMZQXLnS3t25XJjlwgNWmeVSNaGfReWAp+V5lo=
			</data>
		</dict>
		<key>Resources/PIL/BdfFontFile.py</key>
		<dict>
			<key>hash2</key>
			<data>
			PhlZfIRmEfmorbhZZeSM5eebGo1Ei7fL+lR9XlfTZZA=
			</data>
		</dict>
		<key>Resources/PIL/BlpImagePlugin.py</key>
		<dict>
			<key>hash2</key>
			<data>
			Ub4vVKBEniiNBEgNizxScEpO1VKbC1w6iecWUU7T+Vs=
			</data>
		</dict>
		<key>Resources/PIL/BmpImagePlugin.py</key>
		<dict>
			<key>hash2</key>
			<data>
			+SNdj2godmaKYAc08dEng6z3mRPbYYHezjveIR5e+tU=
			</data>
		</dict>
		<key>Resources/PIL/BufrStubImagePlugin.py</key>
		<dict>
			<key>hash2</key>
			<data>
			JSqDhkPNPnFw0Qcz+gQJl+D/iSCFdtcLvPynshKJ4WM=
			</data>
		</dict>
		<key>Resources/PIL/ContainerIO.py</key>
		<dict>
			<key>hash2</key>
			<data>
			wkBqL2GDAb5fh3wrtfTGUfqioJipCl+lg2GxbjQrTZw=
			</data>
		</dict>
		<key>Resources/PIL/CurImagePlugin.py</key>
		<dict>
			<key>hash2</key>
			<data>
			bICiwXZrzSONWBu4bKtshxZSNFj8su0lbDojYntEUYs=
			</data>
		</dict>
		<key>Resources/PIL/DcxImagePlugin.py</key>
		<dict>
			<key>hash2</key>
			<data>
			DhqsmW7MjmnUSTGZ+Skv9hz1XeX3XoQQoAl9GWLAEEY=
			</data>
		</dict>
		<key>Resources/PIL/DdsImagePlugin.py</key>
		<dict>
			<key>hash2</key>
			<data>
			fjdfZK/eQtUp/+bjoRmt+5wgOT5GTmvg6aI+itch4mo=
			</data>
		</dict>
		<key>Resources/PIL/EpsImagePlugin.py</key>
		<dict>
			<key>hash2</key>
			<data>
			ROWwCv08bC/B41eMf2AFe8UW6ZH4/XQ18x12KB/aQLM=
			</data>
		</dict>
		<key>Resources/PIL/ExifTags.py</key>
		<dict>
			<key>hash2</key>
			<data>
			zW6kVikCosiyoCo7J7R62evD3hoxjKPchnVh8po7CZc=
			</data>
		</dict>
		<key>Resources/PIL/FitsImagePlugin.py</key>
		<dict>
			<key>hash2</key>
			<data>
			+oDJnAH113CK5qPvwz9lL81fkV1gla/tNfqLcq8zKgo=
			</data>
		</dict>
		<key>Resources/PIL/FliImagePlugin.py</key>
		<dict>
			<key>hash2</key>
			<data>
			DaWuH8f+9GihS0VVZqF1bT3uDv1Vb0VBl0chnNd82Ow=
			</data>
		</dict>
		<key>Resources/PIL/FontFile.py</key>
		<dict>
			<key>hash2</key>
			<data>
			St7MxO5Q+oakCLWn3ZrgrtaT3wSsmAarxm8AU+G8Moc=
			</data>
		</dict>
		<key>Resources/PIL/FpxImagePlugin.py</key>
		<dict>
			<key>hash2</key>
			<data>
			aXfg0YdvNeJhxqh+f+f22D1NobQ8tSVCj+tpLE2PKfE=
			</data>
		</dict>
		<key>Resources/PIL/FtexImagePlugin.py</key>
		<dict>
			<key>hash2</key>
			<data>
			v2I5YkdfNA3iW35JzKnWry9v6Rgvr0oezGVOuArREac=
			</data>
		</dict>
		<key>Resources/PIL/GbrImagePlugin.py</key>
		<dict>
			<key>hash2</key>
			<data>
			5t0UfLubTPQcuDDbafwC78OLR7IsD5hjpvhUZ5g8z4A=
			</data>
		</dict>
		<key>Resources/PIL/GdImageFile.py</key>
		<dict>
			<key>hash2</key>
			<data>
			LP4Uxv3Y2ivGZIyOVuGJarDDVS7zK6F1Q6SNl4wyGuQ=
			</data>
		</dict>
		<key>Resources/PIL/GifImagePlugin.py</key>
		<dict>
			<key>hash2</key>
			<data>
			SkXboZwxTolq0uteXYX0ncrZiUxyASywqAurOcVAi3U=
			</data>
		</dict>
		<key>Resources/PIL/GimpGradientFile.py</key>
		<dict>
			<key>hash2</key>
			<data>
			Z/4TUYMdPyUsiP40KSIpMJ5yLGMnBaIKOAkHyiQGEWE=
			</data>
		</dict>
		<key>Resources/PIL/GimpPaletteFile.py</key>
		<dict>
			<key>hash2</key>
			<data>
			YHEhKThsEVlXVjFQUnGvhDgNsJcfFqUAN0O0ucG9G+Q=
			</data>
		</dict>
		<key>Resources/PIL/GribStubImagePlugin.py</key>
		<dict>
			<key>hash2</key>
			<data>
			degHg344X3JXL8u+x8NWn08BsmM9wRh+Jg08HHrvfOc=
			</data>
		</dict>
		<key>Resources/PIL/Hdf5StubImagePlugin.py</key>
		<dict>
			<key>hash2</key>
			<data>
			OuEQijGqVwTTSG4dB2vAyQzmN+NYT22tiuZHFH0Q0Sw=
			</data>
		</dict>
		<key>Resources/PIL/IcnsImagePlugin.py</key>
		<dict>
			<key>hash2</key>
			<data>
			qvi+OP0g8CRlNlJE++5/rPlfyxLFLlSOil66Fw4TMwU=
			</data>
		</dict>
		<key>Resources/PIL/IcoImagePlugin.py</key>
		<dict>
			<key>hash2</key>
			<data>
			QCo29Toh08UX8vEcdCAaIeuidSolbPiZlCnQ4rUu2SQ=
			</data>
		</dict>
		<key>Resources/PIL/ImImagePlugin.py</key>
		<dict>
			<key>hash2</key>
			<data>
			wo5OL2PAcQW2MwRkJnS+N16toZzXWL95jx9FBM7l9ok=
			</data>
		</dict>
		<key>Resources/PIL/Image.py</key>
		<dict>
			<key>hash2</key>
			<data>
			95Jefi2QFIfZYOyfHNBRTwBtwrnNZsn5oCsLQsBLdK8=
			</data>
		</dict>
		<key>Resources/PIL/ImageChops.py</key>
		<dict>
			<key>hash2</key>
			<data>
			GEjlymcoDtA5OOeIxQVIX96BD+s6AXhb7TmSLYn2tUg=
			</data>
		</dict>
		<key>Resources/PIL/ImageCms.py</key>
		<dict>
			<key>hash2</key>
			<data>
			A5ZVaTjjxR6AeDNNvK+hmu0QqKOMTscou6BUBTLob0g=
			</data>
		</dict>
		<key>Resources/PIL/ImageColor.py</key>
		<dict>
			<key>hash2</key>
			<data>
			IGA9C2umeED/EzS2Cvj6KsU0VutC9RstWIYPe8uDsVk=
			</data>
		</dict>
		<key>Resources/PIL/ImageDraw.py</key>
		<dict>
			<key>hash2</key>
			<data>
			Enr0ctBHKBnSHVBDlqcIbIAyHgVj5ZbLL+swVb8s8Vo=
			</data>
		</dict>
		<key>Resources/PIL/ImageDraw2.py</key>
		<dict>
			<key>hash2</key>
			<data>
			pdVMW7bVw3KwhXvRZh28Md4y+2xFfuo5fHcDnaYqVK4=
			</data>
		</dict>
		<key>Resources/PIL/ImageEnhance.py</key>
		<dict>
			<key>hash2</key>
			<data>
			4Elhz/lyyxLmx0GkSHrwOAmNJ2TkqVQPHejzGihZUMI=
			</data>
		</dict>
		<key>Resources/PIL/ImageFile.py</key>
		<dict>
			<key>hash2</key>
			<data>
			HLgKqn6K9J4HlnyiPFZUTAfcqxXYjE06fZeKO6V+haw=
			</data>
		</dict>
		<key>Resources/PIL/ImageFilter.py</key>
		<dict>
			<key>hash2</key>
			<data>
			MiTowY9micg1dSfwZkExXSBNPr2b/11kDCGreP6W8x4=
			</data>
		</dict>
		<key>Resources/PIL/ImageFont.py</key>
		<dict>
			<key>hash2</key>
			<data>
			rVQm3zwnTFZ1HSp4OeA5THKjTezhE8HMrnOhHzmqfEM=
			</data>
		</dict>
		<key>Resources/PIL/ImageGrab.py</key>
		<dict>
			<key>hash2</key>
			<data>
			I9PHpsQf2VyNX4T8QL+8awFNotyAzB1mGxTt/I5FbTE=
			</data>
		</dict>
		<key>Resources/PIL/ImageMath.py</key>
		<dict>
			<key>hash2</key>
			<data>
			XasMsgjaD9p2OZa7naOdpEACq3yJl+Q2RGTf4xo7CgM=
			</data>
		</dict>
		<key>Resources/PIL/ImageMode.py</key>
		<dict>
			<key>hash2</key>
			<data>
			5yOxODAZ7jG03DsUFrt7eQayTtIpWPgvfyhlXDWwcv8=
			</data>
		</dict>
		<key>Resources/PIL/ImageMorph.py</key>
		<dict>
			<key>hash2</key>
			<data>
			TowXnk1Q2wX9AXVBDWRRQhCfAbFOUWGMo00vq4yn+fU=
			</data>
		</dict>
		<key>Resources/PIL/ImageOps.py</key>
		<dict>
			<key>hash2</key>
			<data>
			A69qjt+mxDI99387z/4cHI+wtH85SLL/ENTI9EeOQGI=
			</data>
		</dict>
		<key>Resources/PIL/ImagePalette.py</key>
		<dict>
			<key>hash2</key>
			<data>
			M5tYUgadWR7mxUEByyVl7IV9QFFzAGiKKmAhCZtdG0w=
			</data>
		</dict>
		<key>Resources/PIL/ImagePath.py</key>
		<dict>
			<key>hash2</key>
			<data>
			5yUG5XCUil1KKTTA/8PgGhcmg+mnue+GK0FwTBlhjw4=
			</data>
		</dict>
		<key>Resources/PIL/ImageQt.py</key>
		<dict>
			<key>hash2</key>
			<data>
			dQbadF2Lg59OJVjiNVcbz3wvymqEpL+uEZG32b8E+bg=
			</data>
		</dict>
		<key>Resources/PIL/ImageSequence.py</key>
		<dict>
			<key>hash2</key>
			<data>
			gx2EvywPBEjxNJujCqdpbfAm2BpyNV2/f1IaO3niubw=
			</data>
		</dict>
		<key>Resources/PIL/ImageShow.py</key>
		<dict>
			<key>hash2</key>
			<data>
			Ju0/Db2B4/n3yKJV9sDsF7/HAgciEdXlq6I1Eiw1YTo=
			</data>
		</dict>
		<key>Resources/PIL/ImageStat.py</key>
		<dict>
			<key>hash2</key>
			<data>
			S43FZ89r/u4hKCj59lVuWpyVJfhbUy3igXkp9DwaMgM=
			</data>
		</dict>
		<key>Resources/PIL/ImageTk.py</key>
		<dict>
			<key>hash2</key>
			<data>
			b5SntckGXs0ECsI2MmdJg3CSX6AtELsWh0Ohxu41u/k=
			</data>
		</dict>
		<key>Resources/PIL/ImageTransform.py</key>
		<dict>
			<key>hash2</key>
			<data>
			+qek7P3lzLddcXt9cWt5w/L11JGp2yY3AJtOfmJAkDc=
			</data>
		</dict>
		<key>Resources/PIL/ImageWin.py</key>
		<dict>
			<key>hash2</key>
			<data>
			LT05w8/vTfRrC3n9S9pM0TNbXrzZLEJHlCJil7Xv80k=
			</data>
		</dict>
		<key>Resources/PIL/ImtImagePlugin.py</key>
		<dict>
			<key>hash2</key>
			<data>
			SL5IrsHcblltxtX4v/HVFhYnR6haJ0AOd2NHhZKMImY=
			</data>
		</dict>
		<key>Resources/PIL/IptcImagePlugin.py</key>
		<dict>
			<key>hash2</key>
			<data>
			3BVI/oEbFEJC+yn6zmp5Joqf8edCJLKH9N5FQanyaV8=
			</data>
		</dict>
		<key>Resources/PIL/Jpeg2KImagePlugin.py</key>
		<dict>
			<key>hash2</key>
			<data>
			k9UoU7+Hq8vAWi9ZoosA4bfufNJsctBd4ttM1RFxwnk=
			</data>
		</dict>
		<key>Resources/PIL/JpegImagePlugin.py</key>
		<dict>
			<key>hash2</key>
			<data>
			WaCZTpdmzuCM5mi44bNyN4p1EXOsnKz63qv4XEbm8Ns=
			</data>
		</dict>
		<key>Resources/PIL/JpegPresets.py</key>
		<dict>
			<key>hash2</key>
			<data>
			lnqWHo4DLIHIulcdHp0NJ7CWexHt8T3w51kIKlLfkIA=
			</data>
		</dict>
		<key>Resources/PIL/McIdasImagePlugin.py</key>
		<dict>
			<key>hash2</key>
			<data>
			baOIkD+CIIeCgBFTf8kos928PKBuCUqYYa38u3WES/8=
			</data>
		</dict>
		<key>Resources/PIL/MicImagePlugin.py</key>
		<dict>
			<key>hash2</key>
			<data>
			aoIwkWVyr/X+dPvB6ldZOJF3a9kd/OeuEW3say5Y0QM=
			</data>
		</dict>
		<key>Resources/PIL/MpegImagePlugin.py</key>
		<dict>
			<key>hash2</key>
			<data>
			g7BZd93kWpFi41SG/wKFoi0yEPsioI4kj45b2F+3Vrw=
			</data>
		</dict>
		<key>Resources/PIL/MpoImagePlugin.py</key>
		<dict>
			<key>hash2</key>
			<data>
			S45qt7OcY7rBjYlwEk0nUmEj5IOu5z8KVLo066V1RBE=
			</data>
		</dict>
		<key>Resources/PIL/MspImagePlugin.py</key>
		<dict>
			<key>hash2</key>
			<data>
			oxk/MLUDvzJ4JDuOZCHkmqOPXniG42PHOyNGwe60slY=
			</data>
		</dict>
		<key>Resources/PIL/PSDraw.py</key>
		<dict>
			<key>hash2</key>
			<data>
			KMBGj3vXaFpblaIcA9KjFFTpdal41AQggY+UgzqoMkQ=
			</data>
		</dict>
		<key>Resources/PIL/PaletteFile.py</key>
		<dict>
			<key>hash2</key>
			<data>
			suDdAL6VMljXw4oEn1vhTt4DQ4vbpIHGd3A4oxOgE6s=
			</data>
		</dict>
		<key>Resources/PIL/PalmImagePlugin.py</key>
		<dict>
			<key>hash2</key>
			<data>
			WJ1b8I1xTSAXYDJhIpkVFCLu2LlpbiBD5d1Hr+m2l08=
			</data>
		</dict>
		<key>Resources/PIL/PcdImagePlugin.py</key>
		<dict>
			<key>hash2</key>
			<data>
			VweZ108HBHeNEfsoE26EOR4ktxqNGSOWOnd58DhS8Fo=
			</data>
		</dict>
		<key>Resources/PIL/PcfFontFile.py</key>
		<dict>
			<key>hash2</key>
			<data>
			NPZQ0XkbGB8uTlGqgmIPGkwuLMYBdykDeVuvFgIC7JU=
			</data>
		</dict>
		<key>Resources/PIL/PcxImagePlugin.py</key>
		<dict>
			<key>hash2</key>
			<data>
			2dqnjRjSLbjm8Opub4sZRhOIdYLdn3y7Q/ETV8EmiOQ=
			</data>
		</dict>
		<key>Resources/PIL/PdfImagePlugin.py</key>
		<dict>
			<key>hash2</key>
			<data>
			AbJA2f4qzH8G1olfmk18SzQlcx3WsipUYDc5bcR8Wvk=
			</data>
		</dict>
		<key>Resources/PIL/PdfParser.py</key>
		<dict>
			<key>hash2</key>
			<data>
			LnmX0Cm7ZQwGkB1uYP4rvXZUkERmURzmYo78zjeq6VI=
			</data>
		</dict>
		<key>Resources/PIL/PixarImagePlugin.py</key>
		<dict>
			<key>hash2</key>
			<data>
			l/4GwBd0mATnIXYJbwmmODU2vP7wewLu6BRviHCB2EI=
			</data>
		</dict>
		<key>Resources/PIL/PngImagePlugin.py</key>
		<dict>
			<key>hash2</key>
			<data>
			jPBNqZ50txFHWIsDikcdkeeBfLNY1PxT5wzcPMcmcmQ=
			</data>
		</dict>
		<key>Resources/PIL/PpmImagePlugin.py</key>
		<dict>
			<key>hash2</key>
			<data>
			QJM+V+odV7w+prA7B5bLRQcykdC4d7OJ5BBbCvPPIzY=
			</data>
		</dict>
		<key>Resources/PIL/PsdImagePlugin.py</key>
		<dict>
			<key>hash2</key>
			<data>
			ImnNRG4VANs2GATXVEB5Q+yy1Jskc6XRVRtZYi2fALg=
			</data>
		</dict>
		<key>Resources/PIL/QoiImagePlugin.py</key>
		<dict>
			<key>hash2</key>
			<data>
			RPO63QsgHAsyPpcxh7ymeMYlnjVu5gT5ELolkvJt0vc=
			</data>
		</dict>
		<key>Resources/PIL/SgiImagePlugin.py</key>
		<dict>
			<key>hash2</key>
			<data>
			3Ql89s8vycNWjcxJwMw28iksV9Yj2xWoKBQ6c5DHXBg=
			</data>
		</dict>
		<key>Resources/PIL/SpiderImagePlugin.py</key>
		<dict>
			<key>hash2</key>
			<data>
			Bsg6pfZMctas1xYx//oL+ZZseUReZdnLy5a+aKEJhpE=
			</data>
		</dict>
		<key>Resources/PIL/SunImagePlugin.py</key>
		<dict>
			<key>hash2</key>
			<data>
			Hdxkhk0pxpBGxYhPJfCDLwsYcO1KjxjtplNMFYibIvk=
			</data>
		</dict>
		<key>Resources/PIL/TarIO.py</key>
		<dict>
			<key>hash2</key>
			<data>
			BqYUChCBb9F7Sh+uZ86iz1Dtoy2D0obNwGm65z1rdc0=
			</data>
		</dict>
		<key>Resources/PIL/TgaImagePlugin.py</key>
		<dict>
			<key>hash2</key>
			<data>
			2vDsFTcBUBHw1V80wpVv4tgpLDbPr6yVHi6Fvaqf0HY=
			</data>
		</dict>
		<key>Resources/PIL/TiffImagePlugin.py</key>
		<dict>
			<key>hash2</key>
			<data>
			IK7Ur131NNyJET+wk50tzLkSyd7TI1lwSES4N/txy5w=
			</data>
		</dict>
		<key>Resources/PIL/TiffTags.py</key>
		<dict>
			<key>hash2</key>
			<data>
			+gbXLZ5rlHD6crwtY6TkafDm2tamlc5v8e7FjS8PcIg=
			</data>
		</dict>
		<key>Resources/PIL/WalImageFile.py</key>
		<dict>
			<key>hash2</key>
			<data>
			Lfuq/WZ/V/onwucfUc6GWfvY7z/K4s+5EdaQGu/2DD4=
			</data>
		</dict>
		<key>Resources/PIL/WebPImagePlugin.py</key>
		<dict>
			<key>hash2</key>
			<data>
			YFWo6/FYBSrzAf6XMbmrF4YRtR4x7tYecCWF7EA13WQ=
			</data>
		</dict>
		<key>Resources/PIL/WmfImagePlugin.py</key>
		<dict>
			<key>hash2</key>
			<data>
			Z1hzGuHGt08tBLsxgBV7ZVOLdQPykDMYd4RGkw1J8rw=
			</data>
		</dict>
		<key>Resources/PIL/XVThumbImagePlugin.py</key>
		<dict>
			<key>hash2</key>
			<data>
			cJSapkBasFt11O6XYXxqcyA+njxA5BD3wHhNj6VC7Fk=
			</data>
		</dict>
		<key>Resources/PIL/XbmImagePlugin.py</key>
		<dict>
			<key>hash2</key>
			<data>
			Fd6GVDEo73nyFICA3Z3w4LjkwoZWvhHB6rKCm5yVrho=
			</data>
		</dict>
		<key>Resources/PIL/XpmImagePlugin.py</key>
		<dict>
			<key>hash2</key>
			<data>
			jtUKavJCYwIAsJaJwSx8vJsx1oTbCywfDxePENmA93w=
			</data>
		</dict>
		<key>Resources/PIL/__init__.py</key>
		<dict>
			<key>hash2</key>
			<data>
			Q4KOEpR7S/Xsj30fvOsvR94xEpX4KUsVeUwaVP1fU80=
			</data>
		</dict>
		<key>Resources/PIL/__main__.py</key>
		<dict>
			<key>hash2</key>
			<data>
			Lpj4vef8mI7jA1sRCUAoVYaeePD/Uc898xF5c7XLx1A=
			</data>
		</dict>
		<key>Resources/PIL/_avif.cpython-313-darwin.so</key>
		<dict>
			<key>symlink</key>
			<string>../../Frameworks/PIL/_avif.cpython-313-darwin.so</string>
		</dict>
		<key>Resources/PIL/_avif.pyi</key>
		<dict>
			<key>hash2</key>
			<data>
			3fBxcSppJr6EOEcUojvflG3Eegg7lv2Qp0dNQQILrP4=
			</data>
		</dict>
		<key>Resources/PIL/_binary.py</key>
		<dict>
			<key>hash2</key>
			<data>
			pcM6AL04GxgmGeLfcH1V1BZHENwIrQH0uxhJ7r0HIL0=
			</data>
		</dict>
		<key>Resources/PIL/_deprecate.py</key>
		<dict>
			<key>hash2</key>
			<data>
			JYJfJgemvedcdHMH6/RFTDBLNp4vSJqd+o32e3WzNlM=
			</data>
		</dict>
		<key>Resources/PIL/_imaging.cpython-313-darwin.so</key>
		<dict>
			<key>symlink</key>
			<string>../../Frameworks/PIL/_imaging.cpython-313-darwin.so</string>
		</dict>
		<key>Resources/PIL/_imaging.pyi</key>
		<dict>
			<key>hash2</key>
			<data>
			StMbXUZS32AegATP1sUHfs5P05A3TD/BiQKsDHQBW40=
			</data>
		</dict>
		<key>Resources/PIL/_imagingcms.cpython-313-darwin.so</key>
		<dict>
			<key>symlink</key>
			<string>../../Frameworks/PIL/_imagingcms.cpython-313-darwin.so</string>
		</dict>
		<key>Resources/PIL/_imagingcms.pyi</key>
		<dict>
			<key>hash2</key>
			<data>
			brpjxRoiY/2ItyfTrjhKeGEsExe4GPG+25q9AQP8Jp8=
			</data>
		</dict>
		<key>Resources/PIL/_imagingft.cpython-313-darwin.so</key>
		<dict>
			<key>symlink</key>
			<string>../../Frameworks/PIL/_imagingft.cpython-313-darwin.so</string>
		</dict>
		<key>Resources/PIL/_imagingft.pyi</key>
		<dict>
			<key>hash2</key>
			<data>
			IYdFGfApwsqYiJVoD5AVOvgMvnO1eP1J3cMA6L0YZJ0=
			</data>
		</dict>
		<key>Resources/PIL/_imagingmath.cpython-313-darwin.so</key>
		<dict>
			<key>symlink</key>
			<string>../../Frameworks/PIL/_imagingmath.cpython-313-darwin.so</string>
		</dict>
		<key>Resources/PIL/_imagingmath.pyi</key>
		<dict>
			<key>hash2</key>
			<data>
			3fBxcSppJr6EOEcUojvflG3Eegg7lv2Qp0dNQQILrP4=
			</data>
		</dict>
		<key>Resources/PIL/_imagingmorph.cpython-313-darwin.so</key>
		<dict>
			<key>symlink</key>
			<string>../../Frameworks/PIL/_imagingmorph.cpython-313-darwin.so</string>
		</dict>
		<key>Resources/PIL/_imagingmorph.pyi</key>
		<dict>
			<key>hash2</key>
			<data>
			3fBxcSppJr6EOEcUojvflG3Eegg7lv2Qp0dNQQILrP4=
			</data>
		</dict>
		<key>Resources/PIL/_imagingtk.cpython-313-darwin.so</key>
		<dict>
			<key>symlink</key>
			<string>../../Frameworks/PIL/_imagingtk.cpython-313-darwin.so</string>
		</dict>
		<key>Resources/PIL/_imagingtk.pyi</key>
		<dict>
			<key>hash2</key>
			<data>
			3fBxcSppJr6EOEcUojvflG3Eegg7lv2Qp0dNQQILrP4=
			</data>
		</dict>
		<key>Resources/PIL/_tkinter_finder.py</key>
		<dict>
			<key>hash2</key>
			<data>
			GIZ4stmFhUosmHKSrdxcjStiocDNfyJn7RBie2SWxU0=
			</data>
		</dict>
		<key>Resources/PIL/_typing.py</key>
		<dict>
			<key>hash2</key>
			<data>
			1NAWJ7Z59TP98cFv9qGpBMgSHbyR4CAByLjMRRbSZxY=
			</data>
		</dict>
		<key>Resources/PIL/_util.py</key>
		<dict>
			<key>hash2</key>
			<data>
			E76J1WLAe6Xg5yNWYztQwYzxUT/sR/VQxFJu7IZ3S3k=
			</data>
		</dict>
		<key>Resources/PIL/_version.py</key>
		<dict>
			<key>hash2</key>
			<data>
			Zwv2LKWt6v32STL5K9uN7PdcJmZhDlokKTLkDA7Ky1w=
			</data>
		</dict>
		<key>Resources/PIL/_webp.cpython-313-darwin.so</key>
		<dict>
			<key>symlink</key>
			<string>../../Frameworks/PIL/_webp.cpython-313-darwin.so</string>
		</dict>
		<key>Resources/PIL/_webp.pyi</key>
		<dict>
			<key>hash2</key>
			<data>
			3fBxcSppJr6EOEcUojvflG3Eegg7lv2Qp0dNQQILrP4=
			</data>
		</dict>
		<key>Resources/PIL/features.py</key>
		<dict>
			<key>hash2</key>
			<data>
			FfyYObVJbzYQUXf8KuRuqY6kvA8md2LorE81k3EuQrw=
			</data>
		</dict>
		<key>Resources/PIL/py.typed</key>
		<dict>
			<key>hash2</key>
			<data>
			47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=
			</data>
		</dict>
		<key>Resources/PIL/report.py</key>
		<dict>
			<key>hash2</key>
			<data>
			4JY6+IU7sH1RKuRbOvy1fUt0dAoi79FX4tYJN3p1DT0=
			</data>
		</dict>
		<key>Resources/Python</key>
		<dict>
			<key>symlink</key>
			<string>Python.framework/Versions/3.13/Python</string>
		</dict>
		<key>Resources/Python.framework</key>
		<dict>
			<key>symlink</key>
			<string>../Frameworks/Python.framework</string>
		</dict>
		<key>Resources/Tcl</key>
		<dict>
			<key>symlink</key>
			<string>../Frameworks/Tcl</string>
		</dict>
		<key>Resources/Tk</key>
		<dict>
			<key>symlink</key>
			<string>../Frameworks/Tk</string>
		</dict>
		<key>Resources/_tcl_data/auto.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			Z74EtapMCEX4xRQxcazzGtaz8j6xoL6YEzEz6KvJgis=
			</data>
		</dict>
		<key>Resources/_tcl_data/clock.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			P9I20NjNYT3ufk7OI2OWvUJLyBZz6b1AO+5GldD/XHU=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/ascii.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			KUyXF1/QiUCTuGbnNUiuZgru0MPMHnOGfrZuUtNMDdI=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/big5.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			Rlri1IgLgAaxR2zWD6z2dodUOCRMHZOn2+TN4QNedF8=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cns11643.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			CpX2f5CxzdQHp46kAyr565lvw4hkxG10tCs6f37aDIo=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp1250.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			G0Lffn1rD+sXywvI2X5s5omUkjBt2IDEijnRovAnkAQ=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp1251.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			LRvtJCLhMaFACH+vGxK4pG9947ZBO66Lw5XAbw1wubA=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp1252.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			FN864w6B52IL5ru3qeQgg68a4E2UzxIDVl+KPAVCrOA=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp1253.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			IXTZThwdWtk3F7nowgVp7ZWor1Gy06srzpnxqIcEnA4=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp1254.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			vEy+TJn9ZavqRfva8ozB1cQhGSgBJfu9XCwRiSrkYLI=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp1255.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			+L15rlqQ5TkNd9wxyzBlsPk8uIE8nmeszscuLbICegg=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp1256.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			u6zqgdT3o6fzwDYnOkU00x2/i2tcyivMTADLFZPPA9g=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp1257.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			QVFDSnFPyCIoZ3w5sHkIxOGZUvwFjibnw+urdyTODHc=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp1258.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			DNtZ4lXM19z0r4R8mwIK6u54zn/PXyFOvPEjMorPnyQ=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp437.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			1weh8DUUgG5xTwHL/LfJ+Zc6zcgMLWe71Ob4UiOlCVI=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp737.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			i/yjSGmz+aOy/HGwLLrEFRKvbR+KsX0lZOZTIPiO3hA=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp775.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			RPsEtccrWEtig6mbNHiWkMYntQg8XfbotberLGiQPAY=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp850.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			VaotE7eJsxJfXJ0NxbbjqQ15Qm07eCXc1gT1bUxuNqI=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp852.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			Uma28YwxRM+tvLex0n8KfqocZB/TszkF5C5FSf03N3A=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp855.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			4/BxxjrEOvZgYVBu8sV0w1979IVT+1FYrkHZIwwaEN8=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp857.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			45mFxqI4CGtUQnR1UZyeAoV1BwfbUh0YIOY5cjwBw28=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp860.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			bbWROWJ9KavTbzjtLg3iprI0p9fmgcfbr4uIjxysSaU=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp861.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			pDpbWL/Fe9cjsSu96p9uGpITYLNtLVLEIPNymXiEQtM=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp862.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			rqcW1JDDVDliGo8Ayn5Dl+8ccEKOIGxQNrevJfHD2C8=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp863.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			E7XLSB4CFqj8KL+p0PawYM31xFez4SQ1yoJusu9SsGg=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp864.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			6H7AdvlQ/NWBieNi4VBd1VsMj0+n3RqTMcXBEdLOVp8=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp865.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			QH/A/gbSoFfpugEJ6pNWyrOPJ3VtE17zsGqFcFthb1A=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp866.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			gmM2Q80yZUORWsxdKKY0tXlSdM05l005VeUdczC6kzg=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp869.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			J/FuPdArIhLEmA6gm9wGjPAVhKG4u5FFbAP8q6vgkx4=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp874.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			PgZzY/wHZi6+UrphfCqtNkkg8q85WzQWKXQAhZrNeLs=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp932.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			nTPfbhz90s8lU/XidY9FfXEMr/X4xplo8mZazNbppv0=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp936.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			YZMwGSmEqA+TrG8uTl6qRj/T3dx1wfZfOXXzPg3XoLs=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp949.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			Jry2IEckM5YnF3EtBFl6YyZMjkREWUMlZcTBE94KJAs=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp950.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			H7mj1S1DLqLWzUOSfOv59Y8wmiNuGxHSD+jVpfuUTm4=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/dingbats.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			O96a5+r5vnmchLKqToDXi+isusoeSG8Qub3ULjrt3LI=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/ebcdic.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			F6fUXzuC8qQuHTaxPbXO0HeUWj6CcAlHzR+APdKmDb8=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/euc-cn.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			VGOSI39H1xzuHaoarih9lNkyFqH6vWSLUPWd3OforjU=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/euc-jp.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			O/tCxNNtF2NpOu/Oh/YnehGtWnVtaR3tqATZ0O3LMJM=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/euc-kr.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			HxrUxAebM7cG6UinNajDBC9AzGgGXEjCIND1b9BIwzs=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/gb12345.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			DA3xe/7OiXodp3ZcgiRTsJhmVzAozsztE+Lv7gK8zMQ=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/gb1988.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			onF64J4M8tVmwkXcXFiJ0yZmG0DbDV2abZW45rDw51M=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/gb2312-raw.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			7BG/1JxxXNifudOHoHz1QmHg9KHM7BqBDgLHs4rS8oU=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/gb2312.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			VGOSI39H1xzuHaoarih9lNkyFqH6vWSLUPWd3OforjU=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/iso2022-jp.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			4Sko6LV1TUnQ0+eZE13itIC6hLXbqg41DZhG+mf5Q+w=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/iso2022-kr.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			asDxhFpWoaU3uabZvLck3d89Ol5hh5rpJZMbHAU0+7c=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/iso2022.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			dT3aUYp+n23AMJchsfquWMlmH1RYAdqfBHKDkfcL4tA=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/iso8859-1.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			dU72vzpWQiirC1bd45FSHcwabIPPuV1LdhFB5x0ujoc=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/iso8859-10.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			efZHDZvr0wgys6nKWc0f3KKMW+Y3O9AdlJ7uG6Uap6g=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/iso8859-11.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			rZOROf9xQJcL3j2RGA98TXA9f89noCxxOS3hmA3FYOQ=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/iso8859-13.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			M8YHKgBrpOlRPXt/09CLHHRcoQebbXlsNrKlro5K4Cs=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/iso8859-14.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			jXNygyibr4wI7x3X5Hpsd12s5IBBnF4qktbA6Fu1s4E=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/iso8859-15.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			x7A3fzDkIEhJLkcQ/loKVPqYZTlbimdI99rFO5AShPk=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/iso8859-16.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			xAygFLiPl65irhqBbFljse1DKnfYTYnDp2S6FciiNwg=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/iso8859-2.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			DgcySAM4oinMOtTN3gkCGgqBkC3G7ftfEiA+Kv9EZo8=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/iso8859-3.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			czQsJ89V9iXT25DF/I5zQP/fhaUYctv7HQqMseQ+xdo=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/iso8859-4.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			F3Rb3SmXeekdQdsM7ibNxxMto2ZpB6lCELWRztWlWts=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/iso8859-5.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			vF7RZNFTIUBLvcrQ1kfDIv+rFllGIYLb05RUOdnsuuc=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/iso8859-6.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			9QLgeuPxnM3DHkNASc/HM91d+FSHwBYLAzHkAkGtAnQ=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/iso8859-7.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			ATs7KRHGa8HKVOUQgUr0lUwxDaEHN/myokdNcUviqzk=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/iso8859-8.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			4dIHkXqjSD2REOJKDMDNHg5YQ8i/yQHP7npthy3ZRak=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/iso8859-9.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			HNz1EMOEZOUoTtz67DNOP8UWI2wco7mrkcqHjCOGaRQ=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/jis0201.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			SA9h0OGnXe5Zv5pm3gu3j6rk6H/WMX+TSAQSEjJ31EI=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/jis0208.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			EY6hYO8p4RtG3sV68sREBZNN2KfEnSvIuQyU6LqmE4s=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/jis0212.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			JKnTef2jnyvMBYDKPgvS6Zriea9eKEHJ59vn+THRnMA=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/koi8-r.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			cUKxEguZPWCRGXV0CQ/gS+PqZP/DrVoWekteC0LJ8GI=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/koi8-ru.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			CH9ZfSI6CDUmKw+5Y5JGKPxzFrot/7KLc8H+DbAB9TE=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/koi8-t.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			tUIV7d3L88K6E8tLoW+poVKgNbdaSqz936RtFYHy20I=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/koi8-u.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			y68OZCbI0XZoFVKMzxCkOLHz5MWxBXYdo66BCglPKmI=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/ksc5601.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			D4tTCtDey/jdgdqCkbiw+XbGQ7WiktuEaAsx7Pvl0Ao=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/macCentEuro.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			jzCJ9LLKR7esTLeDdbK/rAEmgROnxn0CD4tbfywlu9o=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/macCroatian.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			jQtqiCt0LFzOk4JBMoYGwRHdoMuDM06+3NoXYF82Qa4=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/macCyrillic.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			6xNaiVGfLgBCgt7SGxHDr3zLIyDJdy8t99GkobZ05JE=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/macDingbats.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			j7zGPLKJr6rhW0OHUsF0b0E/O3m6WEXC71K6EQT4vaY=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/macGreek.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			97/5gije2YHsmk0dDaYiR6jSPxWJJuOsvsPM43nJmMI=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/macIceland.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			Yz9ePnW/FZDJSrnL81ONDwp6MZ25AWmTkIRS2QPZxP0=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/macJapan.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			KmhWKY7GKaFr3ZJHEd/j87HjqILd8EtzEHhdg+wNVmw=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/macRoman.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			aPIrrTDaqBshWSVBbBzIM2Czu4fvw0IFiSlzGsZ4/zc=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/macRomania.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			ca6ArftDe3vIjzx2/TcHREmzUm56pXdtK5/VpDwGb6g=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/macThai.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			zq1esrC0TvQAP7yy5JygUDmSuh1lQNEay7uE/bvW55o=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/macTurkish.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			9wO390zG9fqpWfUcdXyUYjZ34nATvK4jvvugGjkmRtk=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/macUkraine.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			TJTn++GDN5gFBW2WCrYk14h55DJ4Ji5Na5ireOX+/qg=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/shiftjis.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			dQJYfVLngQIo8uy0WsQxnqD1wAi3rJEFO5IAENxt35Q=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/symbol.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			c/0rXhQwnYwDbTNPE3ue3x97MtvUVJHPkxhIGFgtBnE=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/tis-620.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			WRi14d7wl4Hv0odRtog2ZaruKfHSRPCJHt7Nqb9qS2M=
			</data>
		</dict>
		<key>Resources/_tcl_data/history.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			4uuL3zlzLKSBc+YdmtpPDQld2qPtH8ziHApSDHVJPY8=
			</data>
		</dict>
		<key>Resources/_tcl_data/http1.0/http.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			8RXZ0r5Yhhdf5hBITqrI4X2f6xxx1BmvZsbjGwwhVSM=
			</data>
		</dict>
		<key>Resources/_tcl_data/http1.0/pkgIndex.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			QgxLMIjJ2s0hvDSAEcrGHXyyg7m+54rnLu12SrCUZRw=
			</data>
		</dict>
		<key>Resources/_tcl_data/init.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			c5Yp7FQiQdIBaeiIo7uYMFLEaRbCjDj8kSapr+itYeQ=
			</data>
		</dict>
		<key>Resources/_tcl_data/opt0.4/optparse.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			BPSy802VnP9HxlAqE30VrJ3E9U1KbnhHj2TIx+FISag=
			</data>
		</dict>
		<key>Resources/_tcl_data/opt0.4/pkgIndex.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			xu8AxVNieCo7jBhehEEyJd3mTOxpD7MkFY1wKMU8N0U=
			</data>
		</dict>
		<key>Resources/_tcl_data/package.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			qzzIxPrjgxdhz3flAQKKSNtuT0/ZmBAnhef8p8nnoUI=
			</data>
		</dict>
		<key>Resources/_tcl_data/parray.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			6+WitMu81/0/em921o14VjAdsBs1DAQJQqe4BqRuABQ=
			</data>
		</dict>
		<key>Resources/_tcl_data/safe.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			vXBUjqIJATWRjy5ho5v91hY+yM74L0AM4iCh7cS+3Ww=
			</data>
		</dict>
		<key>Resources/_tcl_data/tclAppInit.c</key>
		<dict>
			<key>hash2</key>
			<data>
			UAbqOVwsqW50NT6Uhta+PiqFp5JZaaT+QWYvCS75TKo=
			</data>
		</dict>
		<key>Resources/_tcl_data/tclDTrace.d</key>
		<dict>
			<key>hash2</key>
			<data>
			j1Kh00NqHgN5UUIBU7j6Ugnz0NfcnZ2n/IBuDx03hvU=
			</data>
		</dict>
		<key>Resources/_tcl_data/tclIndex</key>
		<dict>
			<key>hash2</key>
			<data>
			52IMPCnEl/bAEZtjsn2V352k19OkeL7xOPtFsaOUUqM=
			</data>
		</dict>
		<key>Resources/_tcl_data/tm.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			SyargiTT5o0QsJW4E02CAfri6JNjXa52gjfdZm+nZR8=
			</data>
		</dict>
		<key>Resources/_tcl_data/word.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			8Y6K4pqo7Sviy7VWi0JD9lT3+wqiBm4Wl3hj7hQVubw=
			</data>
		</dict>
		<key>Resources/_tk_data/bgerror.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			ZESFUunddehLjfY1KeZ/ltuTUdSRPsLpb9Up/kw14Fs=
			</data>
		</dict>
		<key>Resources/_tk_data/button.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			LwE7ZD1i8I3aqh3qOf+A1mB1acnhrMGUBjd7ZNdcz1M=
			</data>
		</dict>
		<key>Resources/_tk_data/choosedir.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			NR84nwkNDP5bEiL4N4VjCh7q5U9oJ0d4RYBbuz0Rn3w=
			</data>
		</dict>
		<key>Resources/_tk_data/clrpick.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			xVB5xDQmWXvoNsi43tBKvauOHNfSuQqJivlSD4AsYEA=
			</data>
		</dict>
		<key>Resources/_tk_data/comdlg.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			9izluEUK57PsK92f32fHf8yZqhjeGV3lM3x1yZeksRw=
			</data>
		</dict>
		<key>Resources/_tk_data/console.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			fhpaiCt966bhPpUEh6w5FS8keTBaQZceuSobBtVvrqk=
			</data>
		</dict>
		<key>Resources/_tk_data/dialog.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			0bHcykYo9h6hUqD6aCAXX2E7w9bpK3OdATKB20huYl0=
			</data>
		</dict>
		<key>Resources/_tk_data/entry.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			kvsv5bIsxpf5OW02gc89McA1mNfY0YWLS3q5dunIClw=
			</data>
		</dict>
		<key>Resources/_tk_data/focus.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			QNThAaZLdTYfdjR5sBIHrnFTUzfnnObhYiZYQvZHHu0=
			</data>
		</dict>
		<key>Resources/_tk_data/fontchooser.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			iqfPB4wm85bhpCGgPQ89BXMeQT/USHUOGS2r/Tqrc8U=
			</data>
		</dict>
		<key>Resources/_tk_data/iconlist.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			Cwh+IFJg1eJUWVpBi6LwnbtV6WefoOjPqZR6eSFDfvw=
			</data>
		</dict>
		<key>Resources/_tk_data/icons.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			TKuArOQQSkFpyb0f2yTWl4ibmEuRopC2mkgZEsOKn9Q=
			</data>
		</dict>
		<key>Resources/_tk_data/images/README</key>
		<dict>
			<key>hash2</key>
			<data>
			JpWt/46QDDG02GQU0iuKSdbdhlyj3Zlnj6NVzcRgk6g=
			</data>
		</dict>
		<key>Resources/_tk_data/images/logo.eps</key>
		<dict>
			<key>hash2</key>
			<data>
			8+d/2UGY7EeDEJNVU2Y46RYvnFeUdTgwdNAkA30Xl9M=
			</data>
		</dict>
		<key>Resources/_tk_data/images/logo100.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			cvazTTyPQk/wopCnk/z780/VYwqRbNAuCl3aAUS1lX8=
			</data>
		</dict>
		<key>Resources/_tk_data/images/logo64.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			E4wkA4IwTzUDg7Au1WxpEDqUMcBUTrHsXc197HpVXdk=
			</data>
		</dict>
		<key>Resources/_tk_data/images/logoLarge.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			D0BHZNB6auLvnh4OjqrCeLfUiNYc8cCEFG8vM7SF8u0=
			</data>
		</dict>
		<key>Resources/_tk_data/images/logoMed.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			TQvTIoq0zD5RWfQze+lp7HtzNOJlyZt2M+Pa88P8+2I=
			</data>
		</dict>
		<key>Resources/_tk_data/images/pwrdLogo.eps</key>
		<dict>
			<key>hash2</key>
			<data>
			KUTrxK8YlJUb+fElD05u34EcIYN0WVDqmoqSZxWILPc=
			</data>
		</dict>
		<key>Resources/_tk_data/images/pwrdLogo100.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			vMDmRYJJQz6MumxYEit8DvqVV8vI+1+Tku7V0lefxws=
			</data>
		</dict>
		<key>Resources/_tk_data/images/pwrdLogo150.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			X8JcMK7nZHfxxOkikxzIBoI98FlSVYP/VwVwXZ6RPBw=
			</data>
		</dict>
		<key>Resources/_tk_data/images/pwrdLogo175.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			YoZulVAcQ2symhVDI1V0PG79ZKN8+2W87ORlq2Ps8kA=
			</data>
		</dict>
		<key>Resources/_tk_data/images/pwrdLogo200.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			utkRY4Y0P0pMOUvbhxRuSfZ09ofVK7hHvZ6BmP2jgsw=
			</data>
		</dict>
		<key>Resources/_tk_data/images/pwrdLogo75.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			RiqP+P0FGoEA6MbAhvSX5AVqzlsgtEeR9Kq5ZLAQpEg=
			</data>
		</dict>
		<key>Resources/_tk_data/images/tai-ku.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			5Tj49JNMpuHOKUFtKSFx8o5n2mxy7Z0ja6QvN0RepB4=
			</data>
		</dict>
		<key>Resources/_tk_data/listbox.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			/f1M8xAtg7rTVW0OAIa18yD3RIS6v5BCHPUm/YufvrA=
			</data>
		</dict>
		<key>Resources/_tk_data/megawidget.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			ZVQxBBVDj0dyKm14mqig/8z0xfaZQSwtacMs/+bRnPQ=
			</data>
		</dict>
		<key>Resources/_tk_data/menu.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			Oek6K9dbkVYjXzJx6gL9fKyCtUKuVt9zPNe9YqLEIH4=
			</data>
		</dict>
		<key>Resources/_tk_data/mkpsenc.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			CsnRHUBG702ObSGfaUG/acauRIxqHC9/w4L4S1eG9mA=
			</data>
		</dict>
		<key>Resources/_tk_data/msgbox.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			mmKB+woZJ9e4H86ev8lSNb2I3xFK2Kh6/qjqawlTM4o=
			</data>
		</dict>
		<key>Resources/_tk_data/msgs/cs.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			0bD+0L6lGz+vCNhjQDTHOIvnFI+bgHRgt9GFcG24QW8=
			</data>
		</dict>
		<key>Resources/_tk_data/msgs/da.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			hebO5gAZJzdnJfkeqlXRez2eOGQ+F3VaQsBf5JHGO94=
			</data>
		</dict>
		<key>Resources/_tk_data/msgs/de.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			Bt11cmJt9csKjTr/usm7dMsSRpB2g21m/RmuW1+rQsc=
			</data>
		</dict>
		<key>Resources/_tk_data/msgs/el.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			HVbQp8B9NLuBZcukf6STUbi8Wp2yRCkLlgHFiF0WFVw=
			</data>
		</dict>
		<key>Resources/_tk_data/msgs/en.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			Zzx2pIraCaFUywOFNL+Q47nAul/WsWGdszUH3mVVM2I=
			</data>
		</dict>
		<key>Resources/_tk_data/msgs/en_gb.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			+5PUVanZzz+CLJaN+yc+2THkM/JJTXHWtfjYPd5+rMI=
			</data>
		</dict>
		<key>Resources/_tk_data/msgs/eo.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			yyS6lZhmcXdqg9zwJWJjgJBx0z7dnAY4Oxn0w2+CCTM=
			</data>
		</dict>
		<key>Resources/_tk_data/msgs/es.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			kdxHGNyFZsNuS80MKSwB9GfKdmHv9gG4cKvN/kqU7Ls=
			</data>
		</dict>
		<key>Resources/_tk_data/msgs/fi.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			9KLE2npnectQiwq7w988lKz3cZt7Ne31VSha24+PzFs=
			</data>
		</dict>
		<key>Resources/_tk_data/msgs/fr.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			Cou7TR/Ye/epDd+lD0ckmUyc540fPpHPQMEXfbeUHcU=
			</data>
		</dict>
		<key>Resources/_tk_data/msgs/hu.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			yBNOrRKeROnFBD4drYGmqQDw3nHbNGjiYDhAA4aH8dg=
			</data>
		</dict>
		<key>Resources/_tk_data/msgs/it.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			n4PdAwntYhEA8xh//NrlC3X1lzu+dK9VCnjvABBJXe0=
			</data>
		</dict>
		<key>Resources/_tk_data/msgs/nl.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			09B6rXksDoP0cEswSTHqVJ0Sy7PZmlc9mBXpVKVxBwc=
			</data>
		</dict>
		<key>Resources/_tk_data/msgs/pl.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			GZO07C3ACdLmyhhdC9Vl0/M6Tvp5uso55Pl/V01j8wU=
			</data>
		</dict>
		<key>Resources/_tk_data/msgs/pt.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			rQ5GYTHTeJ3jIdnQWI4Z5GR7qC7eQe7m6+9GR4b4vb4=
			</data>
		</dict>
		<key>Resources/_tk_data/msgs/ru.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			BhkL8kYjafatTMuN7l9ln4SFbOzPKAbd35UR4BU5DCM=
			</data>
		</dict>
		<key>Resources/_tk_data/msgs/sv.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			t26/ohvB6TegSgTlEivmS1ze4fR8cFi3HYuSPXDDsXs=
			</data>
		</dict>
		<key>Resources/_tk_data/msgs/zh_cn.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			TVsyB7WkBymheinas89+5j5T+cB90FlKvwr4OioBoXg=
			</data>
		</dict>
		<key>Resources/_tk_data/obsolete.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			9pKaXg0YvExmZiBsY6xKqmbtxLn0Vt/AgzAM+pWkS80=
			</data>
		</dict>
		<key>Resources/_tk_data/optMenu.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			dtvb+SFmeNSNFkD4/R4njnFASC4crHaAEnqaQlzGHe4=
			</data>
		</dict>
		<key>Resources/_tk_data/palette.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			YLhXk2i7MGPxbSXwBzhREeDvjZe7KWsDZW3BduNR48o=
			</data>
		</dict>
		<key>Resources/_tk_data/panedwindow.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			St9zixdpFInHHEudmmSxKWGtqGZ7gYVvetvGHf/q3yk=
			</data>
		</dict>
		<key>Resources/_tk_data/pkgIndex.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			HVX+cWUunjO6PHx5Pm6zJeDlTnbPgFoNEH1Bae2vnwk=
			</data>
		</dict>
		<key>Resources/_tk_data/safetk.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			3bDNWcaPwKwhmQCgTfDMtXZEDdiBLEG8oguXRYCGCJI=
			</data>
		</dict>
		<key>Resources/_tk_data/scale.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			QjVGkzJp+uyRMAHyvOMLvbzjkn3J3Zb+dIE+f/snz7U=
			</data>
		</dict>
		<key>Resources/_tk_data/scrlbar.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			GJ5+5LZ4YQAccUpViA2zSs99YmqBbhiwSyMq+ebjPoE=
			</data>
		</dict>
		<key>Resources/_tk_data/spinbox.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			qATIMCngTmvTTTNSYPOIiQ12V961RBc4WL0FAY1zqwE=
			</data>
		</dict>
		<key>Resources/_tk_data/tclIndex</key>
		<dict>
			<key>hash2</key>
			<data>
			5+qDzV3zIvt2DJeHye2pK609BIc3vijSyWBrkkPPC7s=
			</data>
		</dict>
		<key>Resources/_tk_data/tearoff.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			eTDIeB1kOMEeOh8PPTwdBS6SS9+3/ef8F636uc4+QQI=
			</data>
		</dict>
		<key>Resources/_tk_data/text.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			/RK8o8fc7fP8ZkCQKvHJ9IvF3yfJFT7a7BVBPxWB5Kk=
			</data>
		</dict>
		<key>Resources/_tk_data/tk.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			wjqp6TfK9j92DQWgjwP9aozPPWJzTg2y4jrfgxZmpCk=
			</data>
		</dict>
		<key>Resources/_tk_data/tkAppInit.c</key>
		<dict>
			<key>hash2</key>
			<data>
			2dmLyZ2Z0KmIOrUFTe1Rmrf+Rx4NHSRgpUN/I1rIyVE=
			</data>
		</dict>
		<key>Resources/_tk_data/tkfbox.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			tYHxUjBBC/jYxr36NppiZw6JtVYqIhcVaxk7PORnIVM=
			</data>
		</dict>
		<key>Resources/_tk_data/ttk/altTheme.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			6Ve3k9TmCNee02o08iOQtBqtd3rm7MjMvpaqMK7QKZ0=
			</data>
		</dict>
		<key>Resources/_tk_data/ttk/aquaTheme.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			MPMqHxB942195JKZolQ2jUwYAbDN3yWSvdkxFr0pZ6Y=
			</data>
		</dict>
		<key>Resources/_tk_data/ttk/button.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			aE7UCpYJyidc5vPlR/vymA48BA+t2vD9ObfL19WYc2Y=
			</data>
		</dict>
		<key>Resources/_tk_data/ttk/clamTheme.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			UGA1bGydhhGKLTUdLBO63o8N4+cG0SImtpjoqkUxt1w=
			</data>
		</dict>
		<key>Resources/_tk_data/ttk/classicTheme.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			aARyAVwfqIiSYYfSTunT/wNqQOUuWNr0eIYxe7dNtv4=
			</data>
		</dict>
		<key>Resources/_tk_data/ttk/combobox.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			aoWfWH73gfo0g2jaw8rWzO9yftUaJRCIirwq+LyIrnY=
			</data>
		</dict>
		<key>Resources/_tk_data/ttk/cursors.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			wduT6vSCvuG/V8tCPyioxncM7Zs367uMn8htYSFf6Og=
			</data>
		</dict>
		<key>Resources/_tk_data/ttk/defaults.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			3ncV4cG395SQLzCu/LK4ewYnnANfYdE6RdTddvMO2rA=
			</data>
		</dict>
		<key>Resources/_tk_data/ttk/entry.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			nDQXNeF5J5dmJA3Im4O2qeusYIkAZSUf5c9Dj4sU2Tc=
			</data>
		</dict>
		<key>Resources/_tk_data/ttk/fonts.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			Hy0pZbLHKulTwIKaInV3raiVnSRQJjizGGqoQLZ4zDo=
			</data>
		</dict>
		<key>Resources/_tk_data/ttk/menubutton.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			oW37Hq93sFoO+StMx9APVZ+2zwsvc1w6+sUDA8UkoIE=
			</data>
		</dict>
		<key>Resources/_tk_data/ttk/notebook.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			i++TNTpxNENNx4Dup3tS723IU6GT6NgaEy0OJO2TI0s=
			</data>
		</dict>
		<key>Resources/_tk_data/ttk/panedwindow.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			LRLU3zx7KYji4oAXNy61YugUNsYCuLsxxxe90O7BinQ=
			</data>
		</dict>
		<key>Resources/_tk_data/ttk/progress.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			qcNPWV5UfOlO5l4nxBUZXSshBlOp/8+zlVnF4PqcBvg=
			</data>
		</dict>
		<key>Resources/_tk_data/ttk/scale.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			YWHEC/XWz+nINqqyNw78lRvD4UvBfdlHdFW4a1JS1pY=
			</data>
		</dict>
		<key>Resources/_tk_data/ttk/scrollbar.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			ByarHlQbNXE+b3uR+rtP7VwSm4Fg1NXb+Frv+qIY1Us=
			</data>
		</dict>
		<key>Resources/_tk_data/ttk/sizegrip.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			NvrwMr2ccvMHUf6wB3j9IdARCM2x8+pK/QtbtehHMt4=
			</data>
		</dict>
		<key>Resources/_tk_data/ttk/spinbox.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			UTLjkRKRZIbEkBjBv/unDzDfYHsQPiKuXUPHHavoy0A=
			</data>
		</dict>
		<key>Resources/_tk_data/ttk/treeview.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			Qvy3Awa4rUg/omSdfJsTiRsrhB4m0pT1PaMi2kiMYlQ=
			</data>
		</dict>
		<key>Resources/_tk_data/ttk/ttk.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			WGicA1FNGXsus8TM8ZheSPN/IDQ8GOsXneLX9wmbIPM=
			</data>
		</dict>
		<key>Resources/_tk_data/ttk/utils.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			qwchxQyscf8853JpQKrDOQtyMiTNQhrJ6rBqVS8aoVI=
			</data>
		</dict>
		<key>Resources/_tk_data/ttk/vistaTheme.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			CKsS8MbJbKxSTAx1JPjj8z9z686X6NTXEX3QoFBuMZo=
			</data>
		</dict>
		<key>Resources/_tk_data/ttk/winTheme.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			522J8DLSz/Eiw4Xg5SHCmZRq0U9f/v288XZiC0RMDaI=
			</data>
		</dict>
		<key>Resources/_tk_data/ttk/xpTheme.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			poyLNNJVaWdOHOv1Sz9gx7PA+u0qovfTXczLzVGxAbY=
			</data>
		</dict>
		<key>Resources/_tk_data/unsupported.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			402CjnQPFRuWAik0qux7uDQ+I9BA+1TARkGIj1F2frg=
			</data>
		</dict>
		<key>Resources/_tk_data/xmfbox.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			IdJZTIMZ8UxHoPC/PSrKvMgsmrjYSmT6PLYIx+dfWcM=
			</data>
		</dict>
		<key>Resources/base_library.zip</key>
		<dict>
			<key>hash2</key>
			<data>
			Mdf2rCw1N1z85fdbzx7JoICcyJU5Yx+nawbZdU7INyY=
			</data>
		</dict>
		<key>Resources/customtkinter-5.2.2.dist-info/INSTALLER</key>
		<dict>
			<key>hash2</key>
			<data>
			zuuue4knoyJ+UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg=
			</data>
		</dict>
		<key>Resources/customtkinter-5.2.2.dist-info/LICENSE</key>
		<dict>
			<key>hash2</key>
			<data>
			yXO4YA5iAVnLBHPrmwh4MUPg2e1TM/NQ/JMzaNlNng0=
			</data>
		</dict>
		<key>Resources/customtkinter-5.2.2.dist-info/METADATA</key>
		<dict>
			<key>hash2</key>
			<data>
			Ju0LVS8oBVdkzfvV7D4lrmRd/LASEAF5p8PeVYw6DsM=
			</data>
		</dict>
		<key>Resources/customtkinter-5.2.2.dist-info/RECORD</key>
		<dict>
			<key>hash2</key>
			<data>
			SQSCA5aHrddhEwZhZblF49gcv5PyGyBkPba9pIKHbTM=
			</data>
		</dict>
		<key>Resources/customtkinter-5.2.2.dist-info/REQUESTED</key>
		<dict>
			<key>hash2</key>
			<data>
			47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=
			</data>
		</dict>
		<key>Resources/customtkinter-5.2.2.dist-info/WHEEL</key>
		<dict>
			<key>hash2</key>
			<data>
			oiQVh/5PnQM0E3gPdiz09WCNmwiHDMaGer/elqB3coM=
			</data>
		</dict>
		<key>Resources/customtkinter-5.2.2.dist-info/top_level.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			wLXrY2KS8Vj2MnAIH53xH5gHMNCLCKQEmn2QjU9nDH0=
			</data>
		</dict>
		<key>Resources/customtkinter/__init__.py</key>
		<dict>
			<key>hash2</key>
			<data>
			/eLjpybhQB49OORUMIU+op8w0/W4Gxus5AQi3yzQcjg=
			</data>
		</dict>
		<key>Resources/customtkinter/assets/fonts/CustomTkinter_shapes_font.otf</key>
		<dict>
			<key>hash2</key>
			<data>
			+tZ+KwYMMYtshkbQh/vTrdk4tmdiQ/FLDFJiMXlkEnQ=
			</data>
		</dict>
		<key>Resources/customtkinter/assets/fonts/Roboto/Roboto-Medium.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			SsjgNgb/pMN/YaZRCiCA8fN6cFT0cmwhSIfTsj9y42k=
			</data>
		</dict>
		<key>Resources/customtkinter/assets/fonts/Roboto/Roboto-Regular.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			MZz/bnox8PKkHEddykKJCqXRn+FgF+IpD4wdThT3ZIE=
			</data>
		</dict>
		<key>Resources/customtkinter/assets/icons/CustomTkinter_icon_Windows.ico</key>
		<dict>
			<key>hash2</key>
			<data>
			EjTAF8hx6y4g029mj5PgZs3Lk9tGTVzvnXpb+DUG0ow=
			</data>
		</dict>
		<key>Resources/customtkinter/assets/themes/blue.json</key>
		<dict>
			<key>hash2</key>
			<data>
			yUF0cMFs7XpD1sSo4Cevpu3GLCTVrufEwtzRE4WWTTs=
			</data>
		</dict>
		<key>Resources/customtkinter/assets/themes/dark-blue.json</key>
		<dict>
			<key>hash2</key>
			<data>
			alf6b4+4lhowzmQpUisYDXbjr5uODarCWQWYQThqa9M=
			</data>
		</dict>
		<key>Resources/customtkinter/assets/themes/green.json</key>
		<dict>
			<key>hash2</key>
			<data>
			ea2Gv+p/BVesHiCAKJKrtEqWevFbkxWwA5zXXItyp3Y=
			</data>
		</dict>
		<key>Resources/customtkinter/windows/__init__.py</key>
		<dict>
			<key>hash2</key>
			<data>
			eSoo0x/4XkSok/BaQbtM+AtwMRu1za6MPhd6YfWz6sI=
			</data>
		</dict>
		<key>Resources/customtkinter/windows/ctk_input_dialog.py</key>
		<dict>
			<key>hash2</key>
			<data>
			/mAWVEBSPiuBgISbXaWE8LG5Fs7hp0KOM+BmVfBwnp0=
			</data>
		</dict>
		<key>Resources/customtkinter/windows/ctk_tk.py</key>
		<dict>
			<key>hash2</key>
			<data>
			pvYD7mP9SXdVKIomRbNKt0Zu2pFsIOf3246DqPFz1Pg=
			</data>
		</dict>
		<key>Resources/customtkinter/windows/ctk_toplevel.py</key>
		<dict>
			<key>hash2</key>
			<data>
			jEaJpXnAXW6wsdeh3Tr9oABk39GSuk0Z/7OgG8F9Nf8=
			</data>
		</dict>
		<key>Resources/customtkinter/windows/widgets/__init__.py</key>
		<dict>
			<key>hash2</key>
			<data>
			DNohOpF5R2Ae1iP4wNpsDYkOnMRiVrcNdwCPWIxNWGI=
			</data>
		</dict>
		<key>Resources/customtkinter/windows/widgets/appearance_mode/__init__.py</key>
		<dict>
			<key>hash2</key>
			<data>
			fKKK0fVUHFiqmSZw0GZFY0GKgJGDs8A5OXvSBEC3qSg=
			</data>
		</dict>
		<key>Resources/customtkinter/windows/widgets/appearance_mode/appearance_mode_base_class.py</key>
		<dict>
			<key>hash2</key>
			<data>
			nTXJC4vpwjrgCiaOv3V20xVvtkxKR9pVJykIkhWKFz8=
			</data>
		</dict>
		<key>Resources/customtkinter/windows/widgets/appearance_mode/appearance_mode_tracker.py</key>
		<dict>
			<key>hash2</key>
			<data>
			L+X+eLxWFhenWtHr7Z/Mp5msaBTMEIxc+hhKrIVn7bc=
			</data>
		</dict>
		<key>Resources/customtkinter/windows/widgets/core_rendering/__init__.py</key>
		<dict>
			<key>hash2</key>
			<data>
			212WNYQ90/4YY/QB6x0E+xCqqmUXsWegNPQ4VeXSKO0=
			</data>
		</dict>
		<key>Resources/customtkinter/windows/widgets/core_rendering/ctk_canvas.py</key>
		<dict>
			<key>hash2</key>
			<data>
			uXHYqdTns1LAp2GYadyBDcKt5PpNUHka/7l4aV1jqqg=
			</data>
		</dict>
		<key>Resources/customtkinter/windows/widgets/core_rendering/draw_engine.py</key>
		<dict>
			<key>hash2</key>
			<data>
			eeXpsYiCDOqUQo5Pz5nBHDQqg99drt4QDjODmrdee8w=
			</data>
		</dict>
		<key>Resources/customtkinter/windows/widgets/core_widget_classes/__init__.py</key>
		<dict>
			<key>hash2</key>
			<data>
			DsG3zo4hzAUmdFbt3aXgen0Vs0Y9+ppycKTIfrHTYMA=
			</data>
		</dict>
		<key>Resources/customtkinter/windows/widgets/core_widget_classes/ctk_base_class.py</key>
		<dict>
			<key>hash2</key>
			<data>
			PTxPeqvfnct+wuXBqodbGDxpfXJ67Cal201qJxCt3EE=
			</data>
		</dict>
		<key>Resources/customtkinter/windows/widgets/core_widget_classes/dropdown_menu.py</key>
		<dict>
			<key>hash2</key>
			<data>
			dC/SEWk/XQbX47NjLDyhsKh8YKNyPFndoIIxqAnOGRE=
			</data>
		</dict>
		<key>Resources/customtkinter/windows/widgets/ctk_button.py</key>
		<dict>
			<key>hash2</key>
			<data>
			TE/6T88cL8ae1p8ZOy/WE4inpxjP0iaXej0qmjcjbCU=
			</data>
		</dict>
		<key>Resources/customtkinter/windows/widgets/ctk_checkbox.py</key>
		<dict>
			<key>hash2</key>
			<data>
			h8w7SG/9oWE4x4/iBQ+qXvxTq1J/o0Dj4C6JZAqXANU=
			</data>
		</dict>
		<key>Resources/customtkinter/windows/widgets/ctk_combobox.py</key>
		<dict>
			<key>hash2</key>
			<data>
			p1dYS2r9nq7BFfidIbt9JCaZRB8XtjV4XHUV8nGpdGo=
			</data>
		</dict>
		<key>Resources/customtkinter/windows/widgets/ctk_entry.py</key>
		<dict>
			<key>hash2</key>
			<data>
			bxhcZAQt81S59UA5v9fJtsXLLHf9NuUy4qDh4K2m+Ec=
			</data>
		</dict>
		<key>Resources/customtkinter/windows/widgets/ctk_frame.py</key>
		<dict>
			<key>hash2</key>
			<data>
			hcyGh8OzB2CzwCb9c4DMC29QqJS9awzNzEZZmzv5Usc=
			</data>
		</dict>
		<key>Resources/customtkinter/windows/widgets/ctk_label.py</key>
		<dict>
			<key>hash2</key>
			<data>
			4WDRL/gbctV9t+SIKp1oowbvXB+x3CN5o5yajrOogxE=
			</data>
		</dict>
		<key>Resources/customtkinter/windows/widgets/ctk_optionmenu.py</key>
		<dict>
			<key>hash2</key>
			<data>
			5g2OzI6mexzGZxmrUStsyuG+MiihAr+UhyzVS5y5vko=
			</data>
		</dict>
		<key>Resources/customtkinter/windows/widgets/ctk_progressbar.py</key>
		<dict>
			<key>hash2</key>
			<data>
			k8aa/RUIVb+gNUsOUxIHAzBkw8yDgZuU0ar5JeRp4EA=
			</data>
		</dict>
		<key>Resources/customtkinter/windows/widgets/ctk_radiobutton.py</key>
		<dict>
			<key>hash2</key>
			<data>
			hEY0uDuStmkizlpkP8QsfLXRgRNexaj/JPM8/2Iyf3Q=
			</data>
		</dict>
		<key>Resources/customtkinter/windows/widgets/ctk_scrollable_frame.py</key>
		<dict>
			<key>hash2</key>
			<data>
			wKJI1U36m5FhDjIcuDoYPWbTQbLDtusBXu3c1UYQOzk=
			</data>
		</dict>
		<key>Resources/customtkinter/windows/widgets/ctk_scrollbar.py</key>
		<dict>
			<key>hash2</key>
			<data>
			cO6NwExutdSXUBSRdbVve9b0dvV9djUs9Pxetqfxfn8=
			</data>
		</dict>
		<key>Resources/customtkinter/windows/widgets/ctk_segmented_button.py</key>
		<dict>
			<key>hash2</key>
			<data>
			zKPj1J/ZbEXbt5en7vHdZhooqNIrDkr3DKo+QEQ3rvc=
			</data>
		</dict>
		<key>Resources/customtkinter/windows/widgets/ctk_slider.py</key>
		<dict>
			<key>hash2</key>
			<data>
			1xY9vnha+djmym0iLpBkVK30aCDw92lqTMOgZiJ4Hhk=
			</data>
		</dict>
		<key>Resources/customtkinter/windows/widgets/ctk_switch.py</key>
		<dict>
			<key>hash2</key>
			<data>
			aOscR68hoHVkmugnWMqTlsKlLZgGCiO1rfXkDOQSC0Y=
			</data>
		</dict>
		<key>Resources/customtkinter/windows/widgets/ctk_tabview.py</key>
		<dict>
			<key>hash2</key>
			<data>
			ieGuEQqUH7miPQWIkSyon7KVWdbqFFKW5k7oBv53jRg=
			</data>
		</dict>
		<key>Resources/customtkinter/windows/widgets/ctk_textbox.py</key>
		<dict>
			<key>hash2</key>
			<data>
			l+XEQQYVKMB7cTEk4h150b2s+o2HpRh7zHROVc9H3bA=
			</data>
		</dict>
		<key>Resources/customtkinter/windows/widgets/font/__init__.py</key>
		<dict>
			<key>hash2</key>
			<data>
			zYlSsJH+8rL77t7dkz0vXOofHh8NdiIuMQK4V8mfINM=
			</data>
		</dict>
		<key>Resources/customtkinter/windows/widgets/font/ctk_font.py</key>
		<dict>
			<key>hash2</key>
			<data>
			5cdHvujB/tfDlsw9ZRMiGBASvmRSg/WIbSF99x/21vA=
			</data>
		</dict>
		<key>Resources/customtkinter/windows/widgets/font/font_manager.py</key>
		<dict>
			<key>hash2</key>
			<data>
			jwpu61Z00noq0UWcFaHftAMRvXgmKQHEmQiDq+CrqNw=
			</data>
		</dict>
		<key>Resources/customtkinter/windows/widgets/image/__init__.py</key>
		<dict>
			<key>hash2</key>
			<data>
			pzbwlIAV4OE+CkdxJIrl1ELcRg7vbQKkC6HaHLGDNI8=
			</data>
		</dict>
		<key>Resources/customtkinter/windows/widgets/image/ctk_image.py</key>
		<dict>
			<key>hash2</key>
			<data>
			uL/oJrlq8Z134qjiii44Lg03utETSf7rBJ6kgPHeYzk=
			</data>
		</dict>
		<key>Resources/customtkinter/windows/widgets/scaling/__init__.py</key>
		<dict>
			<key>hash2</key>
			<data>
			2GjeG2/A4gq8g7pROKPw6QR2mIH7b6iRDQvT26qz8R4=
			</data>
		</dict>
		<key>Resources/customtkinter/windows/widgets/scaling/scaling_base_class.py</key>
		<dict>
			<key>hash2</key>
			<data>
			VbzzA9GPujOYbEJjDsQRnvoY6v7RSitZwr37lZAkk/8=
			</data>
		</dict>
		<key>Resources/customtkinter/windows/widgets/scaling/scaling_tracker.py</key>
		<dict>
			<key>hash2</key>
			<data>
			cuilXEeSTOylgDRNf2kh+eDQShup+i2Nc7oOcLo3Vtw=
			</data>
		</dict>
		<key>Resources/customtkinter/windows/widgets/theme/__init__.py</key>
		<dict>
			<key>hash2</key>
			<data>
			iZ6T6O+v39hOLfTLuNbTLt0XA/raCYtxwGnmqNB2Lmk=
			</data>
		</dict>
		<key>Resources/customtkinter/windows/widgets/theme/theme_manager.py</key>
		<dict>
			<key>hash2</key>
			<data>
			E+VtzWKZ2IXrc/hvfGUdj50GNr8eII7S/fWBk58xix0=
			</data>
		</dict>
		<key>Resources/customtkinter/windows/widgets/utility/__init__.py</key>
		<dict>
			<key>hash2</key>
			<data>
			STsgOQvThAoUarV9ovM6M40araQUDNU/QmmHbyMaRx0=
			</data>
		</dict>
		<key>Resources/customtkinter/windows/widgets/utility/utility_functions.py</key>
		<dict>
			<key>hash2</key>
			<data>
			d0Wj5Aioxhb3qtC0ITuNZHjwfFHegUfK+bfkDXrrufk=
			</data>
		</dict>
		<key>Resources/img/123.webp</key>
		<dict>
			<key>hash2</key>
			<data>
			zRbt2eaNE2k5bwMRPx6qaxe5d8M6NSKc7Qjkbrb/ODY=
			</data>
		</dict>
		<key>Resources/img/bluetooth.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			AXDfQ7PFxvX7gGQYsojZ3L4PMUU0IIMf0Vbora632Mc=
			</data>
		</dict>
		<key>Resources/img/checkADE.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			1V1jeQug46T0y08D/mYq79KveXSvkHV559jV7HKjgF0=
			</data>
		</dict>
		<key>Resources/img/device.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			/wu7jVkhEKGPoKTYum8zEAUTPQuFfFMXP41QTSed88g=
			</data>
		</dict>
		<key>Resources/img/erase.png</key>
		<dict>
			<key>hash2</key>
			<data>
			7l2sY7T3qXSjQZXIEDtzavqbz2NSrlH4itxtn6/igJc=
			</data>
		</dict>
		<key>Resources/img/exit.png</key>
		<dict>
			<key>hash2</key>
			<data>
			b5d6vlIoz/sJxS4j+nsUsX46dyNReM1kJkVz+QSIJJ8=
			</data>
		</dict>
		<key>Resources/img/findmy.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			6O5TLJgtkHq7LHYtkdu2JJQppDEmUUKnPrSZurSZ3Ew=
			</data>
		</dict>
		<key>Resources/img/icons/atom.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			1BknVeukrHMlT/Gl6GorwKeUD+MPUemaHeiwjlXyZXc=
			</data>
		</dict>
		<key>Resources/img/icons/atom/atom_256x256x32,atom_512x512x32,atom_1024x1024x32.zip</key>
		<dict>
			<key>hash2</key>
			<data>
			Bts3rtQEnl+Cdrpa2uO+M1dhgyWj6CkN9D2K9hP9j30=
			</data>
		</dict>
		<key>Resources/img/icons/atom/atom_256x256x32,atom_512x512x32,atom_1024x1024x32/atom_1024x1024x32.png</key>
		<dict>
			<key>hash2</key>
			<data>
			mreDMWCl033hLKR2RkwOEF0sZOA8cXXMXZ4Ljd9Wdso=
			</data>
		</dict>
		<key>Resources/img/icons/atom/atom_256x256x32,atom_512x512x32,atom_1024x1024x32/atom_256x256x32.png</key>
		<dict>
			<key>hash2</key>
			<data>
			KAvLNDqZB+zPSTVkeezfBYwdr7Hdo4BOCnM0KWcB4KQ=
			</data>
		</dict>
		<key>Resources/img/icons/atom/atom_256x256x32,atom_512x512x32,atom_1024x1024x32/atom_512x512x32.png</key>
		<dict>
			<key>hash2</key>
			<data>
			B6NSQlyAUiboBWlYPp0d19hNx02oMSKr8YaEvlIqvmI=
			</data>
		</dict>
		<key>Resources/img/icons/maccheck_v2.png</key>
		<dict>
			<key>hash2</key>
			<data>
			6CPpvIWWI0ROQnuNNZbpd26SRNSj06TQOm3PxDPETqU=
			</data>
		</dict>
		<key>Resources/img/icons/maccheck_v5.png</key>
		<dict>
			<key>hash2</key>
			<data>
			fXzu0dZao9a8NpU+jJKmmyQ5yjNcUUW0lnOf08Lx7g4=
			</data>
		</dict>
		<key>Resources/img/icons/vsmactool copy.png</key>
		<dict>
			<key>hash2</key>
			<data>
			u/QZAYPv3NEEgr3STpZAeB/cJtHrBdaoCsLmmV/YbGo=
			</data>
		</dict>
		<key>Resources/img/icons/vsmactool-removebg-preview.png</key>
		<dict>
			<key>hash2</key>
			<data>
			7pA4aMtzwk7xhGB2JpXcXkCdT58vOPeIlsSc1ISGJgk=
			</data>
		</dict>
		<key>Resources/img/icons/vsmactool.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			DMB0J8WXdtS5B2TOZHHQajIeDAPe7qvJVwqzRaPQ4BE=
			</data>
		</dict>
		<key>Resources/img/icons/vsmactool.png</key>
		<dict>
			<key>hash2</key>
			<data>
			z+X7mXTFBnTGU7pHxo3aDCGDEcpjhxGQpayYaEvRHmM=
			</data>
		</dict>
		<key>Resources/img/icons/vsmactool_1024.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			AHZOLFaYaGBHw8xFZ+nw4GTaPv4/TqY+9ebLAt+lD40=
			</data>
		</dict>
		<key>Resources/img/icons/vsmactoolv4.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			3eg9xjj0wJjY/r90XeiBoKbNsbwhnz5S8hy6BCghqFw=
			</data>
		</dict>
		<key>Resources/img/icons/xxx.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			lFOvWLdflW1tU5HMS0wAl+vrdmwSApw2D5zRlEO8wzE=
			</data>
		</dict>
		<key>Resources/img/icons/xxx.png</key>
		<dict>
			<key>hash2</key>
			<data>
			T6gwnbchNQ4tJnLwNrEfUyvbOCLS0b4Y4XuTlCJLB7U=
			</data>
		</dict>
		<key>Resources/img/panda.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			LDnYsUI7Tbe+oZvhEh+36wBZLpo3JsKIUU8tq3vcx1s=
			</data>
		</dict>
		<key>Resources/img/shutdown.png</key>
		<dict>
			<key>hash2</key>
			<data>
			adYIbIXC8nbpXDrEmKQUQo+zPZY5R8nBZJbzWaxWFPc=
			</data>
		</dict>
		<key>Resources/img/sysinfo.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			vZn9xKNKjSY5I0arOZQ4oRzT4WN8azNInwkwJN8dE2k=
			</data>
		</dict>
		<key>Resources/img/vsmactool.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			sSmRV17b5zLRgraIiEHDlGI8t1O4nTvWEVyDsMBTSVA=
			</data>
		</dict>
		<key>Resources/keyring-25.6.0.dist-info/INSTALLER</key>
		<dict>
			<key>hash2</key>
			<data>
			zuuue4knoyJ+UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg=
			</data>
		</dict>
		<key>Resources/keyring-25.6.0.dist-info/LICENSE</key>
		<dict>
			<key>hash2</key>
			<data>
			htoPAa6uRjSKPD1GUZXcHOzN55956HdppkuNoEsqR0E=
			</data>
		</dict>
		<key>Resources/keyring-25.6.0.dist-info/METADATA</key>
		<dict>
			<key>hash2</key>
			<data>
			rS2xXSTtNujJISr88RqlBhI4T0Wvx4yWyQLTSGSZ2tM=
			</data>
		</dict>
		<key>Resources/keyring-25.6.0.dist-info/RECORD</key>
		<dict>
			<key>hash2</key>
			<data>
			I/Cxt339GLRsd7s0UZTclOTqF6Rx7AugkjhV4Uspo0M=
			</data>
		</dict>
		<key>Resources/keyring-25.6.0.dist-info/REQUESTED</key>
		<dict>
			<key>hash2</key>
			<data>
			47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=
			</data>
		</dict>
		<key>Resources/keyring-25.6.0.dist-info/WHEEL</key>
		<dict>
			<key>hash2</key>
			<data>
			PZUExdf71Ui/so67QXpySuHtCi3+J3wvF4ORK6k/S8U=
			</data>
		</dict>
		<key>Resources/keyring-25.6.0.dist-info/entry_points.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			8ibyc9zH2ST1JDZHWlQZHEUPx9kVaXfVy8z5af/6OUk=
			</data>
		</dict>
		<key>Resources/keyring-25.6.0.dist-info/top_level.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			ohh1dke28/NdSNkZ6nkVSwIKkLJTOwIfEwnXKva3pkg=
			</data>
		</dict>
		<key>Resources/lib-dynload</key>
		<dict>
			<key>symlink</key>
			<string>../Frameworks/lib-dynload</string>
		</dict>
		<key>Resources/libXau.6.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libXau.6.dylib</string>
		</dict>
		<key>Resources/libavif.16.3.0.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libavif.16.3.0.dylib</string>
		</dict>
		<key>Resources/libbrotlicommon.1.1.0.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libbrotlicommon.1.1.0.dylib</string>
		</dict>
		<key>Resources/libbrotlidec.1.1.0.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libbrotlidec.1.1.0.dylib</string>
		</dict>
		<key>Resources/libcrypto.3.dylib</key>
		<dict>
			<key>symlink</key>
			<string>../Frameworks/libcrypto.3.dylib</string>
		</dict>
		<key>Resources/libfreetype.6.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libfreetype.6.dylib</string>
		</dict>
		<key>Resources/libharfbuzz.0.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libharfbuzz.0.dylib</string>
		</dict>
		<key>Resources/libjpeg.62.4.0.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libjpeg.62.4.0.dylib</string>
		</dict>
		<key>Resources/liblcms2.2.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/liblcms2.2.dylib</string>
		</dict>
		<key>Resources/liblzma.5.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/liblzma.5.dylib</string>
		</dict>
		<key>Resources/libncurses.6.dylib</key>
		<dict>
			<key>symlink</key>
			<string>../Frameworks/libncurses.6.dylib</string>
		</dict>
		<key>Resources/libopenjp2.2.5.3.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libopenjp2.2.5.3.dylib</string>
		</dict>
		<key>Resources/libpng16.16.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libpng16.16.dylib</string>
		</dict>
		<key>Resources/libsharpyuv.0.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libsharpyuv.0.dylib</string>
		</dict>
		<key>Resources/libssl.3.dylib</key>
		<dict>
			<key>symlink</key>
			<string>../Frameworks/libssl.3.dylib</string>
		</dict>
		<key>Resources/libtiff.6.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libtiff.6.dylib</string>
		</dict>
		<key>Resources/libwebp.7.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libwebp.7.dylib</string>
		</dict>
		<key>Resources/libwebpdemux.2.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libwebpdemux.2.dylib</string>
		</dict>
		<key>Resources/libwebpmux.3.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libwebpmux.3.dylib</string>
		</dict>
		<key>Resources/libxcb.1.1.0.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libxcb.1.1.0.dylib</string>
		</dict>
		<key>Resources/libz.1.3.1.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libz.1.3.1.dylib</string>
		</dict>
		<key>Resources/pillow-11.3.0.dist-info/INSTALLER</key>
		<dict>
			<key>hash2</key>
			<data>
			zuuue4knoyJ+UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg=
			</data>
		</dict>
		<key>Resources/pillow-11.3.0.dist-info/METADATA</key>
		<dict>
			<key>hash2</key>
			<data>
			1T1NePio7+GCOWcR73aEA4bSukzNrUIfWMwAw7NAH3M=
			</data>
		</dict>
		<key>Resources/pillow-11.3.0.dist-info/RECORD</key>
		<dict>
			<key>hash2</key>
			<data>
			Ty3YlJ0Va+JMzDioKXlnqwIVNTlzsT8ZWcPByHv18mQ=
			</data>
		</dict>
		<key>Resources/pillow-11.3.0.dist-info/REQUESTED</key>
		<dict>
			<key>hash2</key>
			<data>
			47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=
			</data>
		</dict>
		<key>Resources/pillow-11.3.0.dist-info/WHEEL</key>
		<dict>
			<key>hash2</key>
			<data>
			0rn5ODYhsjI3KEHrk9RXNWLZT7Rowo6wA0Jh2iAIJLk=
			</data>
		</dict>
		<key>Resources/pillow-11.3.0.dist-info/licenses/LICENSE</key>
		<dict>
			<key>hash2</key>
			<data>
			LECn2IlBJv96mTp1JQW5FWlKr+emIIie82isv1S4cUQ=
			</data>
		</dict>
		<key>Resources/pillow-11.3.0.dist-info/top_level.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			riZqrk+hyZqh5f1Z0Zwii3dKfxEsByhu9cU9IODF+NY=
			</data>
		</dict>
		<key>Resources/pillow-11.3.0.dist-info/zip-safe</key>
		<dict>
			<key>hash2</key>
			<data>
			AbpHGcgLb+kRsJGnwFEktk7uzpZOCcBY74+YBdrKVGs=
			</data>
		</dict>
		<key>Resources/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/INSTALLER</key>
		<dict>
			<key>hash2</key>
			<data>
			zuuue4knoyJ+UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg=
			</data>
		</dict>
		<key>Resources/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/LICENSE</key>
		<dict>
			<key>hash2</key>
			<data>
			z8d0m5b2O9McPEK1xHG/dWgUBT6EfBDz6wA0F7xSPTA=
			</data>
		</dict>
		<key>Resources/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/METADATA</key>
		<dict>
			<key>hash2</key>
			<data>
			anuQ7/7h4J1bSEzfcjIBakPi2cyVQ7y7jklLHsBeH1k=
			</data>
		</dict>
		<key>Resources/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/RECORD</key>
		<dict>
			<key>hash2</key>
			<data>
			DY08buueu+hsrH1ghhVSQzwynanqUSSLYdAr4uXmQDA=
			</data>
		</dict>
		<key>Resources/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/REQUESTED</key>
		<dict>
			<key>hash2</key>
			<data>
			47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=
			</data>
		</dict>
		<key>Resources/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/WHEEL</key>
		<dict>
			<key>hash2</key>
			<data>
			mguMlWGMX+VHnMpKOjjQidIo1ssRlCFu4a4mBpz1s2M=
			</data>
		</dict>
		<key>Resources/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/top_level.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			CO3fD9yylANiXkrMo4qHLV/mqXL2sC5JFKgt1yWAT+A=
			</data>
		</dict>
		<key>Resources/setuptools/_vendor/jaraco/text/Lorem ipsum.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			N/7c/79zxOufBY9HZ3yzMgOkNv+TkOTTio4BydrSjgs=
			</data>
		</dict>
		<key>Resources/tcl8/8.4/platform-1.0.19.tm</key>
		<dict>
			<key>hash2</key>
			<data>
			zHmOsmZZyxJGnPkfLnlVW0SXPdZwe/6/eqK52Y2AaoI=
			</data>
		</dict>
		<key>Resources/tcl8/8.4/platform/shell-1.1.4.tm</key>
		<dict>
			<key>hash2</key>
			<data>
			PpoTm30tdoqLlDJ/60AsT8ev7aFW5Gqy0i6MQCZhQqU=
			</data>
		</dict>
		<key>Resources/tcl8/8.5/msgcat-1.6.1.tm</key>
		<dict>
			<key>hash2</key>
			<data>
			VzKVGwK0XDcCwvnufEPeq/z+Cqrok/ibzFnjK9lETOM=
			</data>
		</dict>
		<key>Resources/tcl8/8.5/tcltest-2.5.9.tm</key>
		<dict>
			<key>hash2</key>
			<data>
			WHxo+aEbfWi0gWBaCG0r7rGgYKAfTsRxR6R6953HeGM=
			</data>
		</dict>
		<key>Resources/tcl8/8.6/http-2.9.8.tm</key>
		<dict>
			<key>hash2</key>
			<data>
			v2zRGP2uc+G2j6VUu12WGk3Du24HJLZ3muQBRvCJM3k=
			</data>
		</dict>
		<key>Resources/vsmactool.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			sSmRV17b5zLRgraIiEHDlGI8t1O4nTvWEVyDsMBTSVA=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^Resources/</key>
		<true/>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^(Frameworks|SharedFrameworks|PlugIns|Plug-ins|XPCServices|Helpers|MacOS|Library/(Automator|Spotlight|LoginItems))/</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^[^/]+$</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
